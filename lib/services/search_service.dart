import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../config/pocketbase_config.dart';
import '../models/message/conversation.dart';
import '../models/user.dart';
import 'auth_service_new.dart';

/// 搜索服务
/// 
/// 负责用户搜索、对话搜索、搜索历史管理等功能
class SearchService {
  static final SearchService _instance = SearchService._internal();
  factory SearchService() => _instance;
  SearchService._internal();

  final AuthService _authService = AuthService();
  final _pb = PocketBaseConfig.instance.client;

  // 搜索历史存储键
  static const String _searchHistoryKey = 'search_history';
  
  // 搜索历史缓存
  List<String> _searchHistoryCache = [];

  /// 获取当前用户
  User? get currentUser => _authService.currentUser;

  /// 加载搜索历史
  Future<List<String>> loadSearchHistory() async {
    if (_searchHistoryCache.isNotEmpty) {
      return _searchHistoryCache;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_searchHistoryKey);
      
      if (historyJson != null) {
        final List<dynamic> historyList = json.decode(historyJson);
        _searchHistoryCache = historyList.cast<String>();
      }
      
      return _searchHistoryCache;
    } catch (e) {
      debugPrint('❌ [搜索服务] 加载搜索历史失败: $e');
      return [];
    }
  }

  /// 添加到搜索历史
  Future<void> addToSearchHistory(String query) async {
    if (query.trim().isEmpty) return;

    try {
      // 移除重复项
      _searchHistoryCache.remove(query);
      
      // 添加到开头
      _searchHistoryCache.insert(0, query);
      
      // 限制历史记录数量
      if (_searchHistoryCache.length > 20) {
        _searchHistoryCache = _searchHistoryCache.take(20).toList();
      }

      // 保存到本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_searchHistoryKey, json.encode(_searchHistoryCache));
      
      debugPrint('✅ [搜索服务] 添加搜索历史: $query');
    } catch (e) {
      debugPrint('❌ [搜索服务] 保存搜索历史失败: $e');
    }
  }

  /// 清除搜索历史
  Future<void> clearSearchHistory() async {
    try {
      _searchHistoryCache.clear();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_searchHistoryKey);
      
      debugPrint('✅ [搜索服务] 清除搜索历史');
    } catch (e) {
      debugPrint('❌ [搜索服务] 清除搜索历史失败: $e');
    }
  }

  /// 搜索用户和对话
  Future<Map<String, dynamic>> search(String query) async {
    if (query.trim().isEmpty) {
      return {
        'conversations': <Conversation>[],
        'users': <User>[],
      };
    }

    try {
      // 并行搜索用户和对话
      final results = await Future.wait([
        _searchUsers(query),
        _searchConversations(query),
      ]);

      return {
        'users': results[0],
        'conversations': results[1],
      };
    } catch (e) {
      debugPrint('❌ [搜索服务] 搜索失败: $e');
      return {
        'conversations': <Conversation>[],
        'users': <User>[],
      };
    }
  }

  /// 搜索用户
  Future<List<User>> _searchUsers(String query) async {
    try {
      final records = await _pb.collection('users').getList(
        filter: 'name ~ "$query" || username ~ "$query" || email ~ "$query"',
        perPage: 20,
      );

      final users = <User>[];
      for (final record in records.items) {
        try {
          final user = _parseUser(record);
          if (user != null && user.id != currentUser?.id) {
            users.add(user);
          }
        } catch (e) {
          debugPrint('❌ [搜索服务] 解析用户失败: $e');
        }
      }

      debugPrint('✅ [搜索服务] 搜索到 ${users.length} 个用户');
      return users;
    } catch (e) {
      debugPrint('❌ [搜索服务] 搜索用户失败: $e');
      return [];
    }
  }

  /// 搜索对话
  Future<List<Conversation>> _searchConversations(String query) async {
    final user = currentUser;
    if (user == null) return [];

    try {
      // 搜索对话中的消息内容
      final messageRecords = await _pb.collection('messages').getList(
        filter: 'content ~ "$query" && (sender = "${user.id}" || receiver = "${user.id}")',
        expand: 'sender,receiver',
        perPage: 20,
      );

      // 获取相关的对话ID
      final conversationIds = <String>{};
      for (final record in messageRecords.items) {
        final conversationId = record.data['conversation_id'];
        if (conversationId != null) {
          conversationIds.add(conversationId);
        }
      }

      if (conversationIds.isEmpty) return [];

      // 获取对话详情
      final conversationRecords = await _pb.collection('conversations').getList(
        filter: 'id = "${conversationIds.join('" || id = "')}"',
        expand: 'user1,user2,last_message',
      );

      final conversations = <Conversation>[];
      for (final record in conversationRecords.items) {
        try {
          final conversation = _parseConversation(record, user.id);
          if (conversation != null) {
            conversations.add(conversation);
          }
        } catch (e) {
          debugPrint('❌ [搜索服务] 解析对话失败: $e');
        }
      }

      debugPrint('✅ [搜索服务] 搜索到 ${conversations.length} 个对话');
      return conversations;
    } catch (e) {
      debugPrint('❌ [搜索服务] 搜索对话失败: $e');
      return [];
    }
  }

  /// 通过手机号搜索用户
  Future<List<User>> searchByPhone(String phone) async {
    if (phone.trim().isEmpty) return [];

    try {
      final records = await _pb.collection('users').getList(
        filter: 'phone = "$phone"',
        perPage: 10,
      );

      final users = <User>[];
      for (final record in records.items) {
        try {
          final user = _parseUser(record);
          if (user != null && user.id != currentUser?.id) {
            users.add(user);
          }
        } catch (e) {
          debugPrint('❌ [搜索服务] 解析用户失败: $e');
        }
      }

      debugPrint('✅ [搜索服务] 通过手机号搜索到 ${users.length} 个用户');
      return users;
    } catch (e) {
      debugPrint('❌ [搜索服务] 手机号搜索失败: $e');
      return [];
    }
  }

  /// 解析用户记录
  User? _parseUser(dynamic record) {
    try {
      return User(
        id: record.id,
        username: record.data['username'] ?? '',
        name: record.data['name'] ?? record.data['username'] ?? '未知用户',
        email: record.data['email'] ?? '',
        avatar: record.data['avatar'] != null 
            ? _pb.files.getUrl(record, record.data['avatar']).toString()
            : null,
        phone: record.data['phone'],
        created: DateTime.parse(record.created),
        updated: DateTime.parse(record.updated),
      );
    } catch (e) {
      debugPrint('❌ [搜索服务] 解析用户记录失败: $e');
      return null;
    }
  }

  /// 解析对话记录
  Conversation? _parseConversation(dynamic record, String currentUserId) {
    try {
      final participantA = record.expand?['user1'];
      final participantB = record.expand?['user2'];
      final lastMessage = record.expand?['last_message'];
      
      // 确定对方用户
      final isUserA = participantA?['id'] == currentUserId;
      final otherUser = isUserA ? participantB : participantA;
      
      if (otherUser == null) return null;
      
      // 获取未读数
      final unreadCount = isUserA 
          ? (record.data['user1_unread_count'] ?? 0) as int
          : (record.data['user2_unread_count'] ?? 0) as int;
      
      return Conversation(
        id: record.id,
        otherUserId: otherUser['id'],
        otherUserName: otherUser['name'] ?? otherUser['username'] ?? '未知用户',
        otherUserAvatar: otherUser['avatar'] != null 
            ? _pb.files.getUrl(otherUser, otherUser['avatar']).toString()
            : null,
        lastMessage: lastMessage?['content'],
        lastMessageTime: lastMessage != null 
            ? DateTime.parse(lastMessage['created'])
            : DateTime.parse(record.created),
        lastMessageType: lastMessage?['message_type'] ?? 'text',
        unreadCount: unreadCount,
        isPending: record.data['status'] == 'pending',
      );
    } catch (e) {
      debugPrint('❌ [搜索服务] 解析对话记录失败: $e');
      return null;
    }
  }
}
