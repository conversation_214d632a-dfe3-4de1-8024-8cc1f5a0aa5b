import 'package:flutter/foundation.dart';
import 'service_locator.dart';

/// 消息服务测试类
/// 
/// 用于测试定时轮询和手动刷新功能
class MessageServiceTest {
  static final MessageServiceTest _instance = MessageServiceTest._internal();
  factory MessageServiceTest() => _instance;
  MessageServiceTest._internal();

  /// 测试消息服务功能
  static Future<void> testMessageService() async {
    debugPrint('🧪 [测试] 开始测试消息服务');
    
    try {
      final messageService = Services.message;
      
      // 测试1: 检查服务初始化
      debugPrint('📋 [测试] 1. 检查服务初始化状态');
      debugPrint('   - 轮询状态: ${messageService.isPolling}');
      debugPrint('   - 对话数量: ${messageService.conversationsNotifier.value.length}');
      debugPrint('   - 未读数量: ${messageService.unreadCountNotifier.value}');
      
      // 测试2: 手动刷新
      debugPrint('📋 [测试] 2. 测试手动刷新');
      await messageService.refreshMessages();
      debugPrint('   - 刷新完成');
      
      // 测试3: 监听数据变化
      debugPrint('📋 [测试] 3. 设置数据变化监听');
      messageService.conversationsNotifier.addListener(() {
        debugPrint('   - 对话列表变化: ${messageService.conversationsNotifier.value.length} 个对话');
      });
      
      messageService.unreadCountNotifier.addListener(() {
        debugPrint('   - 未读数量变化: ${messageService.unreadCountNotifier.value}');
      });
      
      // 测试4: 轮询状态
      debugPrint('📋 [测试] 4. 检查轮询状态');
      if (!messageService.isPolling) {
        debugPrint('   - 启动轮询');
        await messageService.startPolling();
      }
      debugPrint('   - 轮询状态: ${messageService.isPolling}');
      
      // 测试5: 等待一个轮询周期
      debugPrint('📋 [测试] 5. 等待轮询周期 (35秒)');
      await Future.delayed(const Duration(seconds: 35));
      debugPrint('   - 轮询周期完成');
      
      debugPrint('✅ [测试] 消息服务测试完成');
      
    } catch (e) {
      debugPrint('❌ [测试] 消息服务测试失败: $e');
    }
  }

  /// 测试发送消息功能
  static Future<void> testSendMessage() async {
    debugPrint('🧪 [测试] 开始测试发送消息功能');
    
    try {
      final messageService = Services.message;
      
      // 测试发送消息
      final message = await messageService.sendMessage(
        'test_user_id',
        '这是一条测试消息',
      );
      
      if (message != null) {
        debugPrint('✅ [测试] 消息发送成功: ${message.content}');
      } else {
        debugPrint('❌ [测试] 消息发送失败');
      }
      
    } catch (e) {
      debugPrint('❌ [测试] 发送消息测试失败: $e');
    }
  }

  /// 测试标记已读功能
  static Future<void> testMarkAsRead() async {
    debugPrint('🧪 [测试] 开始测试标记已读功能');
    
    try {
      final messageService = Services.message;
      
      // 获取第一个对话
      final conversations = messageService.conversationsNotifier.value;
      if (conversations.isNotEmpty) {
        final conversation = conversations.first;
        
        debugPrint('📋 [测试] 标记对话为已读: ${conversation.otherUserName}');
        await messageService.markAsRead(conversation.id);
        debugPrint('✅ [测试] 标记已读成功');
      } else {
        debugPrint('⚠️ [测试] 没有对话可以标记');
      }
      
    } catch (e) {
      debugPrint('❌ [测试] 标记已读测试失败: $e');
    }
  }

  /// 显示服务状态
  static void showServiceStatus() {
    final messageService = Services.message;
    
    debugPrint('📊 [状态] 消息服务状态报告');
    debugPrint('   - 轮询状态: ${messageService.isPolling}');
    debugPrint('   - 对话数量: ${messageService.conversationsNotifier.value.length}');
    debugPrint('   - 未读数量: ${messageService.unreadCountNotifier.value}');
    
    // 显示对话详情
    final conversations = messageService.conversationsNotifier.value;
    for (int i = 0; i < conversations.length && i < 3; i++) {
      final conv = conversations[i];
      debugPrint('   - 对话${i + 1}: ${conv.otherUserName} (未读: ${conv.unreadCount})');
    }
  }

  /// 模拟用户操作测试
  static Future<void> simulateUserOperations() async {
    debugPrint('🎭 [模拟] 开始模拟用户操作');
    
    try {
      // 1. 手动刷新
      debugPrint('🔄 [模拟] 用户下拉刷新');
      await Services.message.refreshMessages();
      await Future.delayed(const Duration(seconds: 2));
      
      // 2. 发送消息
      debugPrint('💬 [模拟] 用户发送消息');
      await testSendMessage();
      await Future.delayed(const Duration(seconds: 2));
      
      // 3. 标记已读
      debugPrint('👁️ [模拟] 用户查看消息');
      await testMarkAsRead();
      await Future.delayed(const Duration(seconds: 2));
      
      // 4. 再次刷新
      debugPrint('🔄 [模拟] 用户再次刷新');
      await Services.message.refreshMessages();
      
      debugPrint('✅ [模拟] 用户操作模拟完成');
      
    } catch (e) {
      debugPrint('❌ [模拟] 用户操作模拟失败: $e');
    }
  }

  /// 性能测试
  static Future<void> performanceTest() async {
    debugPrint('⚡ [性能] 开始性能测试');
    
    try {
      final stopwatch = Stopwatch()..start();
      
      // 测试刷新速度
      await Services.message.refreshMessages();
      stopwatch.stop();
      
      debugPrint('⚡ [性能] 刷新耗时: ${stopwatch.elapsedMilliseconds}ms');
      
      // 测试连续刷新
      stopwatch.reset();
      stopwatch.start();
      
      for (int i = 0; i < 5; i++) {
        await Services.message.refreshMessages();
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      stopwatch.stop();
      debugPrint('⚡ [性能] 5次连续刷新耗时: ${stopwatch.elapsedMilliseconds}ms');
      
    } catch (e) {
      debugPrint('❌ [性能] 性能测试失败: $e');
    }
  }
}
