import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';
import 'dart:io';

/// 位置服务类，用于获取和管理用户位置
class LocationService {
  static const String _lastLocationKey = 'last_location';

  // 默认位置（北京天安门）
  static const LatLng defaultLocation = LatLng(39.9087, 116.3976);

  // 用户当前位置
  LatLng _currentLocation = defaultLocation;

  // 位置变化流控制器
  final StreamController<LatLng> _locationStreamController =
      StreamController<LatLng>.broadcast();

  // 位置变化流
  Stream<LatLng> get locationStream => _locationStreamController.stream;

  // 位置监听器
  StreamSubscription<Position>? _positionStreamSubscription;

  // 单例模式
  static final LocationService _instance = LocationService._internal();

  factory LocationService() {
    return _instance;
  }

  LocationService._internal();

  /// 检查当前平台是否支持位置服务
  bool get _isPlatformSupported {
    if (kIsWeb) return false;
    return Platform.isAndroid || Platform.isIOS;
  }

  /// 初始化位置服务
  Future<void> initialize() async {
    // 首先加载上次保存的位置
    await _loadLastLocation();

    // 检查平台支持
    if (!_isPlatformSupported) {
      debugPrint('当前平台不支持位置服务，使用默认位置');
      return;
    }

    // 异步获取当前位置，不阻塞应用启动
    _updateLocationAsync();
  }

  /// 启动位置监听
  Future<void> startLocationTracking() async {
    if (_positionStreamSubscription != null) {
      return; // 已经在监听
    }

    // 检查平台支持
    if (!_isPlatformSupported) {
      debugPrint('当前平台不支持位置服务，无法启动位置监听');
      return;
    }

    try {
      // 检查权限
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        debugPrint('位置权限被拒绝，无法启动位置监听');
        return;
      }

      // 启动位置流监听
      const locationSettings = LocationSettings(
        accuracy: LocationAccuracy.medium,
        distanceFilter: 10, // 10米变化才触发更新
      );

      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) {
          final newLocation = LatLng(position.latitude, position.longitude);
          _updateLocationInternal(newLocation);
        },
        onError: (error) {
          debugPrint('位置监听错误: $error');
        },
      );

      debugPrint('位置监听已启动');
    } catch (e) {
      debugPrint('启动位置监听失败: $e');
    }
  }

  /// 停止位置监听
  void stopLocationTracking() {
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
    debugPrint('位置监听已停止');
  }

  /// 内部位置更新方法
  void _updateLocationInternal(LatLng newLocation) {
    final distance = calculateDistance(_currentLocation, newLocation);

    // 只有当位置变化超过10米时才更新
    if (distance > 0.01) {
      // 0.01公里 = 10米
      _currentLocation = newLocation;
      _saveLastLocation();

      // 通知监听者
      _locationStreamController.add(newLocation);

      debugPrint(
        '位置已更新: $newLocation (变化距离: ${(distance * 1000).toStringAsFixed(0)}米)',
      );
    }
  }

  /// 清理资源
  void dispose() {
    stopLocationTracking();
    _locationStreamController.close();
  }

  // 缓存上次检查权限的时间，避免频繁检查
  DateTime? _lastPermissionCheck;
  LocationPermission? _cachedPermission;

  /// 异步更新位置信息
  Future<void> _updateLocationAsync() async {
    try {
      // 检查平台支持
      if (!_isPlatformSupported) {
        debugPrint('当前平台不支持位置服务，使用默认位置');
        return;
      }

      // 检查位置服务是否启用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('位置服务未启用');
        return;
      }

      // 检查权限（使用缓存减少频繁检查）
      final now = DateTime.now();
      if (_lastPermissionCheck == null ||
          now.difference(_lastPermissionCheck!).inMinutes > 10 ||
          _cachedPermission == null) {
        // 超过10分钟或首次检查，重新获取权限状态
        _cachedPermission = await Geolocator.checkPermission();
        _lastPermissionCheck = now;
      }

      LocationPermission permission = _cachedPermission!;

      // 处理权限问题
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        _cachedPermission = permission; // 更新缓存
        _lastPermissionCheck = now;

        if (permission == LocationPermission.denied) {
          debugPrint('用户拒绝了位置权限');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('用户永久拒绝了位置权限，请到应用设置中开启');
        return;
      }

      // 权限已授予，获取当前位置
      // 使用较低精度和超时设置以提高性能
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium, // 使用中等精度，提高速度
          timeLimit: Duration(seconds: 5), // 设置超时时间
        ),
      );

      final newLocation = LatLng(position.latitude, position.longitude);

      // 只有当位置变化超过10米时才更新
      if (_currentLocation == defaultLocation ||
          calculateDistance(_currentLocation, newLocation) > 0.01) {
        _currentLocation = newLocation;
        await _saveLastLocation();
        debugPrint('位置已更新: $_currentLocation');
      }
    } catch (e) {
      debugPrint('获取位置失败: $e');
      // 获取位置失败，继续使用上次加载的位置或默认位置
    }
  }

  /// 获取当前位置
  LatLng getCurrentLocation() {
    return _currentLocation;
  }

  /// 设置当前位置
  Future<void> setCurrentLocation(LatLng location) async {
    _currentLocation = location;
    await _saveLastLocation();
    // 通知位置变化
    _locationStreamController.add(_currentLocation);
  }

  /// 设置模拟位置（用于调试）
  Future<void> setMockLocation(LatLng location) async {
    debugPrint('设置模拟位置: ${location.latitude}, ${location.longitude}');
    await setCurrentLocation(location);
  }

  /// 获取一些常用的模拟位置
  static Map<String, LatLng> get mockLocations => {
    '北京天安门': const LatLng(39.9087, 116.3976),
    '上海外滩': const LatLng(31.2397, 121.4999),
    '深圳市民中心': const LatLng(22.5431, 114.0579),
    '杭州西湖': const LatLng(30.2741, 120.1551),
    '成都天府广场': const LatLng(30.6598, 104.0633),
    '广州珠江新城': const LatLng(23.1167, 113.3833),
  };

  /// 计算两点之间的距离（公里）
  double calculateDistance(LatLng point1, LatLng point2) {
    const Distance distance = Distance();
    final double meters = distance(point1, point2);
    return meters / 1000; // 转换为公里
  }

  /// 计算点与当前位置的距离（公里）
  double calculateDistanceFromCurrent(LatLng point) {
    return calculateDistance(_currentLocation, point);
  }

  /// 从本地存储加载上次位置
  Future<void> _loadLastLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final locationString = prefs.getString(_lastLocationKey);

      if (locationString != null) {
        final parts = locationString.split(',');
        if (parts.length == 2) {
          final lat = double.tryParse(parts[0]);
          final lng = double.tryParse(parts[1]);

          if (lat != null && lng != null) {
            _currentLocation = LatLng(lat, lng);
          }
        }
      }
    } catch (e) {
      // 处理错误，使用默认位置
      _currentLocation = defaultLocation;
    }
  }

  /// 保存当前位置到本地存储
  Future<void> _saveLastLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final locationString =
          '${_currentLocation.latitude},${_currentLocation.longitude}';
      await prefs.setString(_lastLocationKey, locationString);
    } catch (e) {
      // 处理保存错误
      debugPrint('保存位置失败: $e');
    }
  }

  // 缓存上次请求位置的时间和结果
  DateTime _lastLocationRequest = DateTime.now().subtract(
    const Duration(minutes: 5),
  );
  LatLng? _lastRequestResult;

  /// 请求更新当前位置
  ///
  /// 为了提高性能，如果短时间内（10秒内）多次请求位置，会返回缓存的结果
  Future<LatLng> requestLocationUpdate() async {
    final now = DateTime.now();

    // 如果距离上次请求不到10秒，且有缓存结果，直接返回缓存
    if (now.difference(_lastLocationRequest).inSeconds < 10 &&
        _lastRequestResult != null) {
      debugPrint('使用缓存的位置结果');
      return _lastRequestResult!;
    }

    try {
      // 使用高精度获取位置
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );

      final newLocation = LatLng(position.latitude, position.longitude);

      // 更新当前位置和缓存
      _currentLocation = newLocation;
      _lastRequestResult = newLocation;
      _lastLocationRequest = now;

      // 保存到本地存储
      await _saveLastLocation();

      return newLocation;
    } catch (e) {
      debugPrint('请求位置更新失败: $e');

      // 更新缓存时间，但保留旧结果
      _lastLocationRequest = now;
      _lastRequestResult = _currentLocation;

      return _currentLocation; // 返回当前位置（可能是上次保存的位置或默认位置）
    }
  }

  /// 将LatLng转换为geoPoint格式
  Map<String, dynamic> latLngToGeoPoint(LatLng latLng) {
    return {
      'lat': latLng.latitude,
      'lon': latLng.longitude,
    };
  }

  /// 将geoPoint格式转换为LatLng
  LatLng geoPointToLatLng(Map<String, dynamic> geoPoint) {
    return LatLng(
      (geoPoint['lat'] as num).toDouble(),
      (geoPoint['lon'] as num).toDouble(),
    );
  }

  /// 计算两个geoPoint之间的距离（公里）
  double calculateDistanceBetweenGeoPoints(
    Map<String, dynamic> geoPoint1,
    Map<String, dynamic> geoPoint2,
  ) {
    final latLng1 = geoPointToLatLng(geoPoint1);
    final latLng2 = geoPointToLatLng(geoPoint2);
    return calculateDistance(latLng1, latLng2);
  }

  /// 检查发布位置是否在钓点50米范围内（实地验证）
  bool isWithinOnSiteRange(
    Map<String, dynamic> spotLocation,
    Map<String, dynamic> publishLocation,
  ) {
    final distance = calculateDistanceBetweenGeoPoints(spotLocation, publishLocation);
    return distance <= 0.05; // 50米 = 0.05公里
  }

  /// 获取当前位置的geoPoint格式
  Map<String, dynamic> getCurrentLocationAsGeoPoint() {
    return latLngToGeoPoint(_currentLocation);
  }

  /// 检查当前位置是否在指定钓点50米范围内
  bool isCurrentLocationWithinOnSiteRange(Map<String, dynamic> spotLocation) {
    final currentGeoPoint = getCurrentLocationAsGeoPoint();
    return isWithinOnSiteRange(spotLocation, currentGeoPoint);
  }

  /// 获取当前位置并返回geoPoint格式（异步版本）
  Future<Map<String, dynamic>> getCurrentLocationAsGeoPointAsync() async {
    final currentLocation = await requestLocationUpdate();
    return latLngToGeoPoint(currentLocation);
  }
}
