import 'package:flutter/material.dart';
import 'snackbar.dart';

/// SnackBarService 使用示例
/// 
/// 这个文件展示了如何使用统一的SnackBarService来显示各种类型的提示消息
class SnackBarUsageExample extends StatelessWidget {
  const SnackBarUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SnackBar使用示例'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'SnackBarService 统一提示服务',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              '特性：\n'
              '• 统一2秒显示时长\n'
              '• 新提示立即覆盖旧提示\n'
              '• 统一样式和图标\n'
              '• 类型化的提示方法',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 32),
            
            // 成功提示
            ElevatedButton.icon(
              onPressed: () {
                SnackBarService.showSuccess(context, '操作成功完成！');
              },
              icon: const Icon(Icons.check_circle),
              label: const Text('显示成功提示'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            
            // 错误提示
            ElevatedButton.icon(
              onPressed: () {
                SnackBarService.showError(context, '操作失败，请重试');
              },
              icon: const Icon(Icons.error),
              label: const Text('显示错误提示'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            
            // 警告提示
            ElevatedButton.icon(
              onPressed: () {
                SnackBarService.showWarning(context, '请注意相关事项');
              },
              icon: const Icon(Icons.warning),
              label: const Text('显示警告提示'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            
            // 信息提示
            ElevatedButton.icon(
              onPressed: () {
                SnackBarService.showInfo(context, '这是一条信息提示');
              },
              icon: const Icon(Icons.info),
              label: const Text('显示信息提示'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            
            // 自定义提示
            ElevatedButton.icon(
              onPressed: () {
                SnackBarService.showCustom(
                  context,
                  '自定义样式的提示',
                  backgroundColor: Colors.purple,
                  icon: Icons.star,
                );
              },
              icon: const Icon(Icons.star),
              label: const Text('显示自定义提示'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(height: 32),
            
            // 测试覆盖功能
            ElevatedButton(
              onPressed: () async {
                // 快速连续显示多个提示，测试覆盖功能
                SnackBarService.showInfo(context, '第一条消息');

                await Future.delayed(const Duration(milliseconds: 500));
                if (context.mounted) {
                  SnackBarService.showWarning(context, '第二条消息（覆盖第一条）');
                }

                await Future.delayed(const Duration(milliseconds: 500));
                if (context.mounted) {
                  SnackBarService.showSuccess(context, '第三条消息（覆盖第二条）');
                }
              },
              child: const Text('测试消息覆盖功能'),
            ),
            
            const SizedBox(height: 32),
            const Divider(),
            const SizedBox(height: 16),
            
            const Text(
              '使用方法：',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. 导入: import \'../widgets/snackbar.dart\';\n'
              '2. 使用: SnackBarService.showSuccess(context, \'消息\');\n'
              '3. 类型: showSuccess, showError, showWarning, showInfo, showCustom',
              style: TextStyle(fontSize: 12, fontFamily: 'monospace'),
            ),
          ],
        ),
      ),
    );
  }
}
