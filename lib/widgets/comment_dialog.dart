import 'package:flutter/material.dart';
import 'snackbar.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// 评论输入对话框
/// 
/// 功能：
/// 1. 评论内容输入
/// 2. 字数限制和提示
/// 3. 提交和取消操作
class CommentDialog extends StatefulWidget {
  final String spotId;
  final String spotName;
  final Function(String comment) onSubmit;

  const CommentDialog({
    super.key,
    required this.spotId,
    required this.spotName,
    required this.onSubmit,
  });

  @override
  State<CommentDialog> createState() => _CommentDialogState();
}

class _CommentDialogState extends State<CommentDialog> {
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isSubmitting = false;
  
  static const int _maxLength = 500; // 最大字数限制

  @override
  void initState() {
    super.initState();
    // 自动聚焦到输入框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _commentController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 提交评论
  Future<void> _submitComment() async {
    final comment = _commentController.text.trim();
    
    if (comment.isEmpty) {
      _showErrorSnackBar('请输入评论内容');
      return;
    }

    if (comment.length > _maxLength) {
      _showErrorSnackBar('评论内容不能超过$_maxLength字');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      await widget.onSubmit(comment);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('❌ [评论对话框] 提交评论失败: $e');
      _showErrorSnackBar('评论发布失败，请稍后重试');
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    if (mounted) {
      SnackBarService.showError(context, message);
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentLength = _commentController.text.length;
    final isOverLimit = currentLength > _maxLength;
    final canSubmit = currentLength > 0 && !isOverLimit && !_isSubmitting;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 500,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                const FaIcon(
                  FontAwesomeIcons.comment,
                  size: 20,
                  color: Colors.blue,
                ),
                const SizedBox(width: 8),
                const Text(
                  '发表评论',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _isSubmitting ? null : () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.grey),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 钓点信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.location_on,
                    size: 16,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      '评论钓点：${widget.spotName}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 评论输入框
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: isOverLimit ? Colors.red : Colors.grey.shade300,
                    width: isOverLimit ? 2 : 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  controller: _commentController,
                  focusNode: _focusNode,
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  enabled: !_isSubmitting,
                  decoration: const InputDecoration(
                    hintText: '写下你的评论...',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.all(12),
                  ),
                  onChanged: (value) {
                    setState(() {
                      // 触发重建以更新字数统计
                    });
                  },
                ),
              ),
            ),

            const SizedBox(height: 12),

            // 字数统计和操作按钮
            Row(
              children: [
                // 字数统计
                Text(
                  '$currentLength/$_maxLength',
                  style: TextStyle(
                    fontSize: 12,
                    color: isOverLimit ? Colors.red : Colors.grey.shade600,
                    fontWeight: isOverLimit ? FontWeight.bold : FontWeight.normal,
                  ),
                ),

                const Spacer(),

                // 取消按钮
                TextButton(
                  onPressed: _isSubmitting ? null : () => Navigator.of(context).pop(),
                  child: Text(
                    '取消',
                    style: TextStyle(
                      color: _isSubmitting ? Colors.grey : Colors.grey.shade600,
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // 发布按钮
                ElevatedButton(
                  onPressed: canSubmit ? _submitComment : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isSubmitting
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('发布'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}