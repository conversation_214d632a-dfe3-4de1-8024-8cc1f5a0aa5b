/// 照片系统组件导出文件
/// 
/// 提供统一的照片展示功能组件导入接口

// 核心组件
export 'photo_gallery.dart';
export 'photo_carousel.dart';
export 'photo_grid.dart';
export 'photo_viewer.dart';

// 数据模型
export 'photo_models.dart';

// 这个文件提供了完整的照片展示功能，包括：
// 
// 1. PhotoGallery - 照片画廊主组件
//    - 统一的照片展示接口
//    - 支持多种显示模式（轮播、网格、列表、紧凑）
//    - 自动管理查看器
//    - 支持自定义配置
// 
// 2. PhotoCarousel - 照片轮播组件
//    - 支持轮播和紧凑模式
//    - 支持点击查看大图
//    - 支持自动播放
//    - 支持页面指示器
// 
// 3. PhotoGrid - 照片网格组件
//    - 网格展示照片
//    - 支持最大显示数量限制
//    - 支持"更多"按钮
//    - 支持点击查看大图
// 
// 4. PhotoViewer - 全屏照片查看器
//    - 全屏显示照片
//    - 支持缩放和滑动
//    - 支持轮播切换
//    - 支持关闭按钮
// 
// 5. PhotoItem - 通用照片数据模型
//    - 统一不同来源的照片数据
//    - 支持从SpotPhoto转换
//    - 支持从URL创建
// 
// 使用示例：
// 
// ```dart
// // 轮播画廊
// PhotoGallery.carousel(
//   photos: photos,
//   height: 280,
//   enableViewer: true,
// )
// 
// // 网格画廊
// PhotoGallery.grid(
//   photos: photos,
//   crossAxisCount: 3,
//   maxDisplayCount: 6,
//   onViewMore: () => showAllPhotos(),
// )
// 
// // 紧凑画廊
// PhotoGallery.compact(
//   photos: photos,
//   height: 120,
// )
// 
// // 从SpotPhoto转换
// final photoItems = spotPhotos
//     .map((p) => PhotoItem.fromSpotPhoto(p))
//     .toList();
// ```
