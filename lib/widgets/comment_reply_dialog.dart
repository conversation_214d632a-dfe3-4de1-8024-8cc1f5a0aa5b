import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/spot_comment.dart';
import 'snackbar.dart';

/// 评论回复对话框
class CommentReplyDialog extends StatefulWidget {
  final String spotId;
  final SpotComment parentComment;
  final Function(String content, String parentCommentId, String replyToUserId, String replyToUsername) onSubmit;

  const CommentReplyDialog({
    super.key,
    required this.spotId,
    required this.parentComment,
    required this.onSubmit,
  });

  @override
  State<CommentReplyDialog> createState() => _CommentReplyDialogState();
}

class _CommentReplyDialogState extends State<CommentReplyDialog> {
  final TextEditingController _replyController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isSubmitting = false;
  
  static const int _maxLength = 500; // 最大字数限制

  @override
  void initState() {
    super.initState();
    // 自动聚焦到输入框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _replyController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 提交回复
  Future<void> _submitReply() async {
    final reply = _replyController.text.trim();
    
    if (reply.isEmpty) {
      _showErrorSnackBar('请输入回复内容');
      return;
    }

    if (reply.length > _maxLength) {
      _showErrorSnackBar('回复内容不能超过$_maxLength字');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      await widget.onSubmit(
        reply,
        widget.parentComment.id,
        widget.parentComment.userId,
        widget.parentComment.username,
      );
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('❌ [回复对话框] 提交回复失败: $e');
      _showErrorSnackBar('回复发布失败，请稍后重试');
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    if (mounted) {
      SnackBarService.showError(context, message);
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentLength = _replyController.text.length;
    final isOverLimit = currentLength > _maxLength;
    final canSubmit = currentLength > 0 && !isOverLimit && !_isSubmitting;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 500,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                const Icon(
                  FontAwesomeIcons.reply,
                  size: 20,
                  color: Colors.blue,
                ),
                const SizedBox(width: 8),
                const Text(
                  '回复评论',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 原评论预览
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 12,
                        backgroundColor: Colors.grey.shade300,
                        child: Icon(
                          Icons.person,
                          size: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.parentComment.username,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.parentComment.content,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey.shade800,
                      height: 1.3,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 回复输入框
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: isOverLimit ? Colors.red : Colors.grey.shade300,
                    width: isOverLimit ? 2 : 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  controller: _replyController,
                  focusNode: _focusNode,
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  enabled: !_isSubmitting,
                  decoration: InputDecoration(
                    hintText: '回复 @${widget.parentComment.username}...',
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(12),
                  ),
                  onChanged: (value) {
                    setState(() {
                      // 触发重建以更新字数统计
                    });
                  },
                ),
              ),
            ),

            const SizedBox(height: 12),

            // 底部操作栏
            Row(
              children: [
                // 字数统计
                Text(
                  '$currentLength/$_maxLength',
                  style: TextStyle(
                    fontSize: 12,
                    color: isOverLimit ? Colors.red : Colors.grey.shade600,
                  ),
                ),
                
                const Spacer(),
                
                // 取消按钮
                TextButton(
                  onPressed: _isSubmitting ? null : () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                
                const SizedBox(width: 8),
                
                // 发布按钮
                ElevatedButton(
                  onPressed: canSubmit ? _submitReply : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                  ),
                  child: _isSubmitting
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('回复'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
