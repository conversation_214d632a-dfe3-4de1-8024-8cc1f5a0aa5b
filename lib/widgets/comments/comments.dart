/// 评论系统组件导出文件
/// 
/// 提供统一的评论功能组件导入接口

// 核心组件
export 'comment_system.dart';
export 'comment_input_bar.dart';
export 'comment_list_view.dart';

// 这个文件提供了完整的评论系统功能，包括：
// 
// 1. CommentSystem - 完整的评论系统组件
//    - 支持多种评论类型（钓点、帖子、活动）
//    - 集成评论列表和输入栏
//    - 自动管理回复状态
// 
// 2. CommentInputBar - 独立的评论输入栏组件
//    - 支持普通评论和回复评论
//    - 自动管理输入状态
//    - 支持自定义样式
// 
// 3. CommentListView - 评论列表视图组件
//    - 显示评论列表
//    - 支持回复展开/收起
//    - 支持点赞功能
//    - 自动管理加载状态
// 
// 使用示例：
// 
// ```dart
// // 完整的评论系统
// CommentSystem(
//   targetId: spotId,
//   type: CommentType.spot,
//   title: '钓点评论',
// )
// 
// // 仅评论列表
// CommentListView(
//   targetId: spotId,
//   onReply: (comment) => handleReply(comment),
// )
// 
// // 仅输入栏
// CommentInputBar(
//   onSubmit: (content) => submitComment(content),
// )
// ```
