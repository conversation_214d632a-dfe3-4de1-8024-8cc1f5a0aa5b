import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import '../models/fishing_spot.dart';
import '../services/service_locator.dart';
import 'spot_detail_page.dart';

// 带距离信息的钓点类
class _SpotWithDistance {
  final FishingSpot spot;
  final double distance;

  _SpotWithDistance({required this.spot, required this.distance});
}

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<FishingSpot> _allSpots = [];
  List<FishingSpot> _filteredSpots = [];
  List<FishingSpot> _displayedSpots = [];

  bool _isLoading = true;
  bool _isLoadingMore = false;
  final int _pageSize = 10;

  // 用户当前位置
  late final LatLng _userLocation;

  // 导航标签
  final List<String> _primaryTabs = ['精选', '热门好物', '新品抢先', '品牌钓具'];
  final List<String> _secondaryTabs = ['公告', '全国', '附近', '问答', '技巧'];
  int _selectedPrimaryTab = 0;
  int _selectedSecondaryTab = 2; // 默认选中"附近"

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _initializeData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  // 滚动监听器
  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _loadMoreSpots();
    }
  }

  // 初始化数据
  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
    });

    // 初始化位置服务
    await Services.location.initialize();

    // 获取用户当前位置
    _userLocation = Services.location.getCurrentLocation();

    // 加载钓点数据
    await _loadSpots();
  }

  // 加载钓点数据
  Future<void> _loadSpots() async {
    setState(() {
      _isLoading = true;
    });

    // 只搜索附近的钓点，而不是所有钓点
    final userLocation = Services.location.getCurrentLocation();
    const double searchRange = 0.1; // 约11公里搜索范围

    final spots = await Services.fishingSpot.getSpotsInBounds(
      minLat: userLocation.latitude - searchRange,
      maxLat: userLocation.latitude + searchRange,
      minLng: userLocation.longitude - searchRange,
      maxLng: userLocation.longitude + searchRange,
      limit: 50, // 限制搜索结果数量
    );

    // 计算每个钓点与用户的距离
    final spotsWithDistance =
        spots.map((spot) {
          final distance = Services.location.calculateDistance(
            _userLocation,
            spot.locationLatLng,
          );
          return _SpotWithDistance(spot: spot, distance: distance);
        }).toList();

    // 按距离排序
    spotsWithDistance.sort((a, b) => a.distance.compareTo(b.distance));

    // 提取排序后的钓点
    final sortedSpots = spotsWithDistance.map((item) => item.spot).toList();

    setState(() {
      _allSpots = sortedSpots;
      _filteredSpots = sortedSpots;
      _loadMoreSpots(initial: true);
      _isLoading = false;
    });
  }

  // 加载更多钓点
  void _loadMoreSpots({bool initial = false}) {
    if (_isLoadingMore && !initial) return;

    setState(() {
      _isLoadingMore = true;
    });

    final start = initial ? 0 : _displayedSpots.length;
    final end = start + _pageSize;

    if (start < _filteredSpots.length) {
      final newSpots = _filteredSpots.sublist(
        start,
        end > _filteredSpots.length ? _filteredSpots.length : end,
      );

      setState(() {
        if (initial) {
          _displayedSpots = newSpots;
        } else {
          _displayedSpots.addAll(newSpots);
        }
        _isLoadingMore = false;
      });
    } else {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  // 搜索钓点
  void _searchSpots(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredSpots = _allSpots;
        _loadMoreSpots(initial: true);
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();
    final filtered =
        _allSpots.where((spot) {
          return spot.name.toLowerCase().contains(lowercaseQuery) ||
              spot.description.toLowerCase().contains(lowercaseQuery) ||
              spot.sharedBy.toLowerCase().contains(lowercaseQuery);
        }).toList();

    setState(() {
      _filteredSpots = filtered;
      _loadMoreSpots(initial: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Column(
          children: [
            // 搜索框区域
            _buildSearchHeader(),
            // 导航标签区域
            _buildNavigationTabs(),
            // 内容列表
            Expanded(child: _buildContentList()),
          ],
        ),
      ),
    );
  }

  // 构建搜索头部
  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
              ),
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: '搜钓点，帖文，心得',
                  hintStyle: TextStyle(color: Colors.grey, fontSize: 14),
                  prefixIcon: Icon(Icons.search, color: Colors.grey, size: 20),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                ),
                onChanged: _searchSpots,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Center(
              child: Text(
                '发布',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建导航标签
  Widget _buildNavigationTabs() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 主标签栏
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children:
                  _primaryTabs.asMap().entries.map((entry) {
                    final index = entry.key;
                    final tab = entry.value;
                    final isSelected = index == _selectedPrimaryTab;

                    return Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedPrimaryTab = index;
                          });
                        },
                        child: Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color:
                                    isSelected
                                        ? Colors.blue
                                        : Colors.transparent,
                                width: 2,
                              ),
                            ),
                          ),
                          child: Text(
                            tab,
                            style: TextStyle(
                              color:
                                  isSelected ? Colors.blue : Colors.grey[600],
                              fontSize: 14,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
          // 分隔线
          Container(height: 1, color: Colors.grey[200]),
          // 二级标签栏
          Container(
            height: 45,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children:
                  _secondaryTabs.asMap().entries.map((entry) {
                    final index = entry.key;
                    final tab = entry.value;
                    final isSelected = index == _selectedSecondaryTab;

                    return Padding(
                      padding: const EdgeInsets.only(right: 24),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedSecondaryTab = index;
                          });
                        },
                        child: Container(
                          alignment: Alignment.center,
                          child: Text(
                            tab,
                            style: TextStyle(
                              color:
                                  isSelected ? Colors.blue : Colors.grey[600],
                              fontSize: 14,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  // 构建内容列表
  Widget _buildContentList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_filteredSpots.isEmpty) {
      return const Center(
        child: Text(
          '没有找到匹配的钓点',
          style: TextStyle(color: Colors.grey, fontSize: 16),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _displayedSpots.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        // 显示加载更多指示器
        if (index == _displayedSpots.length) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          );
        }

        final spot = _displayedSpots[index];
        return _buildSpotCard(spot);
      },
    );
  }

  // 构建钓点卡片
  Widget _buildSpotCard(FishingSpot spot) {
    final distance = Services.location.calculateDistanceFromCurrent(
      spot.locationLatLng,
    );
    final distanceText = '${distance.toStringAsFixed(1)}km';

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showSpotDetails(spot),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                spot.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),

              // 描述
              Text(
                spot.description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // 图片区域（模拟3张图片）
              _buildImageRow(),
              const SizedBox(height: 12),

              // 底部信息
              Row(
                children: [
                  // 用户头像和昵称
                  CircleAvatar(
                    radius: 12,
                    backgroundColor: Colors.blue[100],
                    child: Text(
                      spot.sharedBy.isNotEmpty
                          ? spot.sharedBy[0].toUpperCase()
                          : 'U',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          spot.sharedBy,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text(
                              _formatDate(spot.createdAt),
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey[500],
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              Icons.location_on,
                              size: 12,
                              color: Colors.grey[500],
                            ),
                            const SizedBox(width: 2),
                            Text(
                              distanceText,
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // 互动数据
                  Row(
                    children: [
                      Icon(
                        Icons.chat_bubble_outline,
                        size: 16,
                        color: Colors.grey[500],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${spot.comments.length}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      const SizedBox(width: 16),
                      Icon(
                        Icons.favorite_border,
                        size: 16,
                        color: Colors.grey[500],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${spot.likes}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建图片行（模拟3张图片）
  Widget _buildImageRow() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 80,
            margin: const EdgeInsets.only(right: 4),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.image, color: Colors.grey[400], size: 32),
          ),
        ),
        Expanded(
          child: Container(
            height: 80,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.image, color: Colors.grey[400], size: 32),
          ),
        ),
        Expanded(
          child: Container(
            height: 80,
            margin: const EdgeInsets.only(left: 4),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.image, color: Colors.grey[400], size: 32),
          ),
        ),
      ],
    );
  }

  // 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  // 显示钓点详情
  void _showSpotDetails(FishingSpot spot) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => SpotDetailPage(spot: spot)),
    );
  }
}
