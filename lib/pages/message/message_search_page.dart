import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/message/conversation.dart';
import '../../models/user.dart';
import '../../services/service_locator.dart';
import 'conversation_detail_page.dart';

/// 消息搜索页面
class MessageSearchPage extends StatefulWidget {
  const MessageSearchPage({super.key});

  @override
  State<MessageSearchPage> createState() => _MessageSearchPageState();
}

class _MessageSearchPageState extends State<MessageSearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  List<String> _searchHistory = [];
  List<Conversation> _searchResults = [];
  List<User> _userResults = [];
  bool _isSearching = false;
  bool _showResults = false;

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();

    // 自动聚焦搜索框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  /// 加载搜索历史
  Future<void> _loadSearchHistory() async {
    try {
      final history = await Services.search.loadSearchHistory();
      if (mounted) {
        setState(() {
          _searchHistory = history;
        });
      }
    } catch (e) {
      debugPrint('加载搜索历史失败: $e');
    }
  }

  /// 执行搜索
  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _showResults = false;
        _searchResults = [];
        _userResults = [];
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _showResults = true;
    });

    try {
      // 使用搜索服务进行搜索
      final results = await Services.search.search(query);

      if (mounted) {
        setState(() {
          _searchResults = results['conversations'] ?? [];
          _userResults = results['users'] ?? [];
          _isSearching = false;
        });

        // 添加到搜索历史
        _addToSearchHistory(query);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('搜索失败: $e')));
      }
    }
  }

  /// 添加到搜索历史
  void _addToSearchHistory(String query) {
    Services.search.addToSearchHistory(query);
    if (!_searchHistory.contains(query)) {
      setState(() {
        _searchHistory.insert(0, query);
        if (_searchHistory.length > 10) {
          _searchHistory = _searchHistory.take(10).toList();
        }
      });
    }
  }

  /// 清除搜索历史
  void _clearSearchHistory() {
    Services.search.clearSearchHistory();
    setState(() {
      _searchHistory.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        title: Container(
          height: 36,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(18),
          ),
          child: TextField(
            controller: _searchController,
            focusNode: _searchFocusNode,
            decoration: const InputDecoration(
              hintText: '搜索消息或联系人',
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              suffixIcon: Icon(Icons.search, size: 20),
            ),
            textInputAction: TextInputAction.search,
            onChanged: (value) {
              // 实时搜索
              if (value.trim().isNotEmpty) {
                _performSearch(value);
              } else {
                setState(() {
                  _showResults = false;
                });
              }
            },
            onSubmitted: _performSearch,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
        ],
      ),
      body: _showResults ? _buildSearchResults() : _buildSearchHome(),
    );
  }

  /// 搜索首页（显示历史和快速操作）
  Widget _buildSearchHome() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索历史
          if (_searchHistory.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '搜索历史',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                TextButton(
                  onPressed: _clearSearchHistory,
                  child: Text(
                    '清空',
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  _searchHistory.map((keyword) {
                    return GestureDetector(
                      onTap: () {
                        _searchController.text = keyword;
                        _performSearch(keyword);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Text(
                          keyword,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
            const SizedBox(height: 32),
          ],

          // 快速操作
          const Text(
            '快速操作',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildQuickActionItem(
                  icon: FontAwesomeIcons.qrcode,
                  title: '扫一扫加好友',
                  subtitle: '扫描二维码快速添加好友',
                  onTap: () {
                    _scanQRCode();
                  },
                ),
                const Divider(height: 1, indent: 56),
                _buildQuickActionItem(
                  icon: FontAwesomeIcons.phone,
                  title: '手机号添加好友',
                  subtitle: '通过手机号搜索并添加好友',
                  onTap: () {
                    _showAddByPhoneDialog();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 快速操作项
  Widget _buildQuickActionItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(icon, color: Theme.of(context).primaryColor, size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(fontSize: 13, color: Colors.grey[600]),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey[400],
      ),
      onTap: onTap,
    );
  }

  /// 搜索结果页面
  Widget _buildSearchResults() {
    if (_isSearching) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_searchResults.isEmpty && _userResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              FontAwesomeIcons.magnifyingGlass,
              size: 64,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 16),
            Text(
              '未找到相关结果',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[500],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '尝试使用其他关键词',
              style: TextStyle(fontSize: 14, color: Colors.grey[400]),
            ),
          ],
        ),
      );
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // 对话结果
        if (_searchResults.isNotEmpty) ...[
          const Text(
            '对话',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children:
                  _searchResults.map((conversation) {
                    return _buildConversationResultItem(conversation);
                  }).toList(),
            ),
          ),
          const SizedBox(height: 24),
        ],

        // 用户结果
        if (_userResults.isNotEmpty) ...[
          const Text(
            '用户',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children:
                  _userResults.map((user) {
                    return _buildUserResultItem(user);
                  }).toList(),
            ),
          ),
        ],
      ],
    );
  }

  /// 对话搜索结果项
  Widget _buildConversationResultItem(Conversation conversation) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: CircleAvatar(
        radius: 20,
        backgroundColor: Colors.grey[300],
        backgroundImage:
            conversation.otherUserAvatar != null
                ? NetworkImage(conversation.otherUserAvatar!)
                : null,
        child:
            conversation.otherUserAvatar == null
                ? Text(
                  conversation.otherUserName.isNotEmpty
                      ? conversation.otherUserName[0]
                      : '?',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                )
                : null,
      ),
      title: Text(
        conversation.otherUserName,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        conversation.displayLastMessage,
        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Text(
        conversation.displayTime,
        style: TextStyle(fontSize: 12, color: Colors.grey[500]),
      ),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => ConversationDetailPage(conversation: conversation),
          ),
        );
      },
    );
  }

  /// 用户搜索结果项
  Widget _buildUserResultItem(User user) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: CircleAvatar(
        radius: 20,
        backgroundColor: Colors.grey[300],
        backgroundImage:
            user.avatar != null ? NetworkImage(user.avatar!) : null,
        child:
            user.avatar == null
                ? Text(
                  user.name.isNotEmpty ? user.name[0] : '?',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                )
                : null,
      ),
      title: Text(
        user.name,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        '用户ID: ${user.id}',
        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
      ),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(15),
        ),
        child: const Text(
          '发消息',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      onTap: () {
        _createOrNavigateToConversation(user);
      },
    );
  }

  /// 创建新对话或跳转到现有对话
  void _createOrNavigateToConversation(User user) async {
    try {
      // 检查是否已有对话
      final conversations = Services.message.conversationsNotifier.value;
      final existingConversation = conversations.firstWhere(
        (conv) => conv.otherUserId == user.id,
        orElse: () => throw StateError('No conversation found'),
      );

      // 如果已有对话，直接跳转
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) =>
                    ConversationDetailPage(conversation: existingConversation),
          ),
        );
      }
    } catch (e) {
      // 没有现有对话，创建新对话
      try {
        // 发送一条初始消息来创建对话
        final message = await Services.message.sendMessage(user.id, '你好！');

        if (message != null && mounted) {
          // 创建临时对话对象
          final newConversation = Conversation(
            id: 'temp_${user.id}',
            otherUserId: user.id,
            otherUserName: user.name,
            otherUserAvatar: user.avatar,
            lastMessage: '你好！',
            lastMessageTime: DateTime.now(),
            unreadCount: 0,
            isPending: false,
          );

          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) =>
                      ConversationDetailPage(conversation: newConversation),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('创建对话失败: $e')));
        }
      }
    }
  }

  /// 扫码功能
  void _scanQRCode() async {
    try {
      // 这里应该使用qr_code_scanner插件来扫描二维码
      // final result = await Navigator.push(
      //   context,
      //   MaterialPageRoute(builder: (context) => QRScannerPage()),
      // );

      // 暂时显示提示
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('扫码功能需要安装qr_code_scanner插件')),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('扫码失败: $e')));
    }
  }

  /// 显示手机号添加好友对话框
  void _showAddByPhoneDialog() {
    final phoneController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('手机号添加好友'),
            content: TextField(
              controller: phoneController,
              decoration: const InputDecoration(
                hintText: '请输入手机号',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () async {
                  final phone = phoneController.text.trim();
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  navigator.pop();

                  if (phone.isNotEmpty) {
                    setState(() {
                      _isSearching = true;
                      _showResults = true;
                    });

                    try {
                      final users = await Services.search.searchByPhone(phone);
                      if (mounted) {
                        setState(() {
                          _userResults = users;
                          _searchResults = [];
                          _isSearching = false;
                        });
                      }
                    } catch (e) {
                      if (mounted) {
                        setState(() {
                          _isSearching = false;
                        });
                        scaffoldMessenger.showSnackBar(
                          SnackBar(content: Text('搜索失败: $e'))
                        );
                      }
                    }
                  }
                },
                child: const Text('搜索'),
              ),
            ],
          ),
    );
  }
}
