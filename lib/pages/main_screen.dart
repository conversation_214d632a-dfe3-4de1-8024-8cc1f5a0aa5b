import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'home_page.dart';
import 'search_page.dart';
import 'message/message_main_page.dart';
import 'profile_page.dart';
import '../services/service_locator.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with WidgetsBindingObserver {
  int _currentIndex = 0;
  bool _showBottomNavigationBar = true; // 控制底部导航栏显示

  // 页面列表
  late final List<Widget> _pages;

  // 使用新的服务架构
  // 通过Services便捷访问器访问认证服务

  @override
  void initState() {
    super.initState();

    // 添加应用生命周期监听
    WidgetsBinding.instance.addObserver(this);

    // 直接创建页面列表
    _pages = [
      HomePage(
        onBottomNavigationBarVisibilityChanged: (visible) {
          debugPrint('🔍 [MainScreen] 收到导航栏显示状态变更请求: $visible');
          debugPrint('🔍 [MainScreen] 当前页面索引: $_currentIndex');
          // 只有当前显示的是HomePage（index=0）时才处理导航栏隐藏
          if (_currentIndex == 0) {
            setState(() {
              _showBottomNavigationBar = visible;
              debugPrint(
                '🔍 [MainScreen] 导航栏状态已更新为: $_showBottomNavigationBar',
              );
            });
          } else {
            debugPrint('🔍 [MainScreen] 当前不在HomePage，忽略导航栏状态变更请求');
          }
        },
      ),
      const SearchPage(),
      const MessageMainPage(),
      const ProfilePage(),
    ];
  }

  @override
  void dispose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        // 应用从后台恢复时，静默尝试刷新登录状态
        _refreshAuthStateOnResume();
        break;
      case AppLifecycleState.paused:
        // 应用切换到后台时的处理
        debugPrint('应用切换到后台');
        break;
      case AppLifecycleState.detached:
        // 应用即将终止时的处理
        debugPrint('应用即将终止');
        break;
      case AppLifecycleState.inactive:
        // 应用失去焦点时的处理
        debugPrint('应用失去焦点');
        break;
      case AppLifecycleState.hidden:
        // 应用被隐藏时的处理
        debugPrint('应用被隐藏');
        break;
    }
  }

  /// 应用恢复时刷新认证状态
  Future<void> _refreshAuthStateOnResume() async {
    try {
      // 静默尝试刷新登录状态，不显示任何错误提示
      if (!Services.auth.isLoggedIn) {
        await Services.auth.initialize();
      }
    } catch (e) {
      // 静默处理错误，不影响用户体验
      debugPrint('应用恢复时刷新认证状态失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // 只在导航栏状态变化时打印调试信息
    return PopScope(
      canPop: false, // 阻止默认的返回行为
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // 按返回键时将应用切换到后台，而不是退出
          _moveAppToBackground();
        }
      },
      child: Scaffold(
        body: IndexedStack(index: _currentIndex, children: _pages),
        bottomNavigationBar:
            _showBottomNavigationBar
                ? SizedBox(
                  height: 50, // [*参数调整*]底部导航栏整体高度
                  child: BottomNavigationBar(
                    backgroundColor: Colors.white,
                    currentIndex: _currentIndex,
                    onTap: (index) {
                      setState(() {
                        final oldIndex = _currentIndex;
                        _currentIndex = index;

                        // 如果切换到HomePage，检查是否需要隐藏导航栏
                        if (index == 0 && oldIndex != 0) {
                          // 切换到HomePage时，导航栏状态由HomePage自己控制
                          debugPrint('🔍 [MainScreen] 切换到HomePage');
                        }

                        // 如果从HomePage切换到其他页面，确保显示导航栏
                        if (oldIndex == 0 && index != 0) {
                          _showBottomNavigationBar = true;
                          debugPrint(
                            '🔍 [MainScreen] 从HomePage切换到其他页面，强制显示导航栏',
                          );
                        }
                      });
                    },
                    type: BottomNavigationBarType.fixed,
                    selectedItemColor: Theme.of(context).primaryColor,
                    unselectedItemColor: Colors.grey,
                    showSelectedLabels: true,
                    showUnselectedLabels: true,
                    iconSize: 18, // 缩小图标尺寸
                    selectedFontSize: 10, // 缩小选中标签字体
                    unselectedFontSize: 10, // 缩小未选中标签字体
                    items: const [
                      BottomNavigationBarItem(
                        icon: FaIcon(FontAwesomeIcons.house, size: 18),
                        label: '主页',
                      ),
                      BottomNavigationBarItem(
                        icon: FaIcon(
                          FontAwesomeIcons.magnifyingGlass,
                          size: 18,
                        ),
                        label: '搜索',
                      ),
                      BottomNavigationBarItem(
                        icon: FaIcon(FontAwesomeIcons.message, size: 18),
                        label: '消息',
                      ),
                      BottomNavigationBarItem(
                        icon: FaIcon(FontAwesomeIcons.user, size: 18),
                        label: '我的',
                      ),
                    ],
                  ),
                )
                : null,
      ),
    );
  }

  /// 将应用切换到后台
  void _moveAppToBackground() {
    SystemNavigator.pop();
  }
}
