import 'dart:async';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/fishing_spot.dart';
import '../models/spot_photo.dart';
import '../services/service_locator.dart';
import '../services/baidu_map_navigation_service.dart';
import '../services/unified_image_service.dart';
import '../widgets/comments/comments.dart';
import '../widgets/photos/photos.dart';
import '../widgets/snackbar.dart';
import '../widgets/carousel_height_manager.dart';
import '../config/pocketbase_config.dart';

/// 钓点详情全屏页面
///
/// 特性：
/// - 全屏显示，完美支持键盘避让
/// - SnackBar显示正常
/// - 更好的用户体验和交互空间
/// - 标准的页面结构，易于维护
class SpotDetailPage extends StatefulWidget {
  final FishingSpot spot;

  const SpotDetailPage({super.key, required this.spot});

  @override
  State<SpotDetailPage> createState() => _SpotDetailPageState();
}

class _SpotDetailPageState extends State<SpotDetailPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // 照片相关
  List<SpotPhoto> _photos = [];
  bool _isLoadingPhotos = true;

  // 照片轮播控制器
  late PageController _photoPageController;
  Timer? _autoPlayTimer;
  bool _userInteracted = false;
  int _currentPhotoIndex = 0;

  // 标签页控制器
  late TabController _tabController;

  // 滚动控制器
  late ScrollController _scrollController;

  // 用户互动状态
  bool _isLiked = false;
  bool _isUnliked = false;
  bool _isFavorited = false;

  // 实时数据
  int _likesCount = 0;
  int _unlikesCount = 0;
  int _commentsCount = 0;

  // 键盘状态
  bool _isKeyboardVisible = false;

  // 轮播图高度管理器
  late CarouselHeightManager _carouselHeightManager;

  // 统一图片服务
  final UnifiedImageService _imageService = UnifiedImageService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _tabController = TabController(length: 3, vsync: this);
    _scrollController = ScrollController();
    _photoPageController = PageController();
    _carouselHeightManager = CarouselHeightManager();

    // 监听标签页变化
    _tabController.addListener(_onTabChanged);

    _loadSpotData();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _autoPlayTimer?.cancel();
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _scrollController.dispose();
    _photoPageController.dispose();
    _carouselHeightManager.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        // 应用进入后台时暂停自动播放
        _stopAutoPlay();
        break;
      case AppLifecycleState.resumed:
        // 应用恢复时重新开始自动播放
        if (_photos.length > 1 && !_userInteracted) {
          _startAutoPlay();
        }
        break;
      case AppLifecycleState.hidden:
        _stopAutoPlay();
        break;
    }
  }

  /// 加载钓点数据
  Future<void> _loadSpotData() async {
    await Future.wait([
      _loadSpotPhotos(),
      _loadUserInteractions(),
      _loadSpotStatistics(),
    ]);
  }

  /// 加载钓点照片
  Future<void> _loadSpotPhotos() async {
    try {
      debugPrint('🔍 [钓点详情] 开始加载钓点照片，钓点ID: ${widget.spot.id}');

      // 从PocketBase直接获取钓点照片
      final pb = PocketBaseConfig.instance.client;
      final records = await pb
          .collection('spot_photos')
          .getFullList(
            filter: 'spot_id = "${widget.spot.id}"',
            sort: 'sort_order,created',
          );

      if (mounted) {
        setState(() {
          _photos =
              records
                  .map((record) => SpotPhoto.fromJson(record.toJson()))
                  .toList();
          _isLoadingPhotos = false;
        });
        debugPrint('✅ [钓点详情] 照片加载完成，共 ${_photos.length} 张');

        // 如果有多张照片，启动自动播放
        if (_photos.length > 1) {
          _startAutoPlay();
        }
      }
    } catch (e) {
      debugPrint('❌ [钓点详情] 加载钓点照片失败: $e');
      if (mounted) {
        setState(() {
          _isLoadingPhotos = false;
        });
      }
    }
  }

  /// 加载用户互动状态
  Future<void> _loadUserInteractions() async {
    try {
      debugPrint('🔍 [互动状态] 开始加载用户互动状态');

      // 确保服务已加载用户互动记录
      await Services.spotInteraction.loadUserInteractions();

      if (mounted) {
        setState(() {
          _isLiked = Services.spotInteraction.isLiked(widget.spot.id);
          _isUnliked = Services.spotInteraction.isUnliked(widget.spot.id);
          _isFavorited = Services.userFavorite.isFavorited(widget.spot.id);
        });

        debugPrint('✅ [互动状态] 状态加载完成: 点赞=$_isLiked, 倒赞=$_isUnliked');
      }
    } catch (e) {
      debugPrint('❌ [互动状态] 加载用户互动状态失败: $e');
      if (mounted) {
        setState(() {
          _isLiked = false;
          _isUnliked = false;
          _isFavorited = false;
        });
      }
    }
  }

  /// 加载钓点统计数据
  Future<void> _loadSpotStatistics() async {
    try {
      debugPrint('🔍 [统计数据] 开始加载钓点统计数据');

      // 获取点赞数量
      final likesCount = await Services.social.getSpotLikesCount(
        widget.spot.id,
      );

      // 获取评论数量
      final commentsCount = await Services.spotComment.getCommentCount(
        widget.spot.id,
      );

      if (mounted) {
        setState(() {
          _likesCount = likesCount;
          _unlikesCount = widget.spot.unlikes; // 暂时使用模型中的数据
          _commentsCount = commentsCount;
        });

        debugPrint(
          '✅ [统计数据] 数据加载完成: 点赞=$_likesCount, 倒赞=$_unlikesCount, 评论=$_commentsCount',
        );
      }
    } catch (e) {
      debugPrint('❌ [统计数据] 加载统计数据失败: $e');
      // 使用默认值或钓点模型中的数据作为后备
      if (mounted) {
        setState(() {
          _likesCount = widget.spot.likes;
          _unlikesCount = widget.spot.unlikes;
          _commentsCount = widget.spot.comments.length;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 监听键盘状态
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    // 键盘状态变化时的处理
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('⌨️ [键盘状态] 检查键盘状态变化');
      debugPrint('⌨️ [键盘状态] 键盘高度: $keyboardHeight');
      debugPrint('⌨️ [键盘状态] 当前状态: $_isKeyboardVisible -> $isKeyboardVisible');

      if (_isKeyboardVisible != isKeyboardVisible) {
        debugPrint('⌨️ [键盘状态] 键盘状态发生变化，更新状态');
        setState(() {
          _isKeyboardVisible = isKeyboardVisible;
        });

        // 更新轮播图高度管理器的键盘状态
        debugPrint('⌨️ [键盘状态] 准备更新轮播图高度管理器');
        _carouselHeightManager.updateKeyboardState(isKeyboardVisible);

        // 键盘收起时，滚动到顶部显示图片轮播
        if (!isKeyboardVisible && _scrollController.hasClients) {
          debugPrint('⌨️ [键盘状态] 键盘收起，滚动到顶部');
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      } else {
        debugPrint('⌨️ [键盘状态] 键盘状态未变化');
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true, // 启用键盘避让
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 自定义AppBar和照片展示区域
          _buildSliverAppBar(),

          // 标签页导航
          SliverPersistentHeader(
            pinned: true,
            delegate: _TabBarDelegate(
              tabBar: TabBar(
                controller: _tabController,
                labelColor: Colors.blue,
                unselectedLabelColor: Colors.grey,
                indicatorColor: Colors.blue,
                tabs: const [Tab(text: '详情'), Tab(text: '照片'), Tab(text: '评论')],
              ),
            ),
          ),

          // 标签页内容
          SliverFillRemaining(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildDetailsTab(),
                _buildPhotosTab(),
                _buildCommentsTab(),
              ],
            ),
          ),
        ],
      ),

      // 底部操作栏
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  /// 标签页变化监听
  void _onTabChanged() {
    debugPrint('📑 [标签页] 标签页变化监听触发');
    debugPrint('📑 [标签页] indexIsChanging: ${_tabController.indexIsChanging}');
    debugPrint('📑 [标签页] 当前索引: ${_tabController.index}');

    if (!_tabController.indexIsChanging) {
      PageType pageType;
      switch (_tabController.index) {
        case 0:
          pageType = PageType.details;
          debugPrint('📑 [标签页] 切换到详情页');
          break;
        case 1:
          pageType = PageType.photos;
          debugPrint('📑 [标签页] 切换到照片页');
          break;
        case 2:
          pageType = PageType.comments;
          debugPrint('📑 [标签页] 切换到评论页');
          break;
        default:
          pageType = PageType.details;
          debugPrint('📑 [标签页] 默认切换到详情页');
      }
      debugPrint('📑 [标签页] 准备更新轮播图高度管理器');
      _carouselHeightManager.updatePageType(pageType);
    } else {
      debugPrint('📑 [标签页] 标签页正在切换中，跳过处理');
    }
  }

  /// 构建SliverAppBar和照片展示区域
  Widget _buildSliverAppBar() {
    return AnimatedBuilder(
      animation: _carouselHeightManager,
      builder: (context, child) {
        debugPrint('🎨 [AnimatedBuilder] 重建SliverAppBar');
        debugPrint(
          '🎨 [AnimatedBuilder] 当前高度: ${_carouselHeightManager.currentHeight}',
        );
        debugPrint(
          '🎨 [AnimatedBuilder] 当前档位: ${_carouselHeightManager.currentLevel}',
        );

        return SliverAppBar(
          expandedHeight: _carouselHeightManager.currentHeight,
          floating: false,
          pinned: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          leading: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.95),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              color: Colors.black87,
              iconSize: 20,
            ),
          ),
          actions: [
            Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.95),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: Icon(
                  _isFavorited ? Icons.favorite : Icons.favorite_border,
                  color: _isFavorited ? Colors.red : Colors.black87,
                ),
                onPressed: _handleFavorite,
                iconSize: 20,
              ),
            ),
            Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.95),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: const Icon(Icons.share),
                onPressed: _handleShare,
                color: Colors.black87,
                iconSize: 20,
              ),
            ),
          ],
          flexibleSpace: FlexibleSpaceBar(
            title: Text(
              widget.spot.name,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    offset: Offset(0, 1),
                    blurRadius: 3,
                    color: Colors.black54,
                  ),
                ],
              ),
            ),
            background: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: _carouselHeightManager.currentHeight,
              child: _buildPhotoCarousel(),
            ),
          ),
        );
      },
    );
  }

  /// 构建照片轮播
  Widget _buildPhotoCarousel() {
    if (_isLoadingPhotos) {
      return Container(
        color: Colors.grey.shade200,
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_photos.isEmpty) {
      return Container(
        color: Colors.grey.shade200,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.photo_library_outlined, size: 48, color: Colors.grey),
              SizedBox(height: 8),
              Text('暂无照片', style: TextStyle(color: Colors.grey, fontSize: 16)),
            ],
          ),
        ),
      );
    }

    return Stack(
      children: [
        // 照片轮播
        PageView.builder(
          controller: _photoPageController,
          onPageChanged: _onPhotoPageChanged,
          itemCount: _photos.length,
          itemBuilder: (context, index) {
            final photo = _photos[index];
            return GestureDetector(
              onTap: () {
                // 点击照片时显示大图
                _showPhotoViewer(index);
              },
              onPanStart: (_) {
                // 用户开始滑动时触发
                _onUserSwipe();
              },
              child: SizedBox(
                width: double.infinity,
                height: 300,
                child: _imageService.buildCachedSignedImage(
                  originalUrl: photo.url,
                  fit: BoxFit.cover,
                  placeholder: Container(
                    color: Colors.grey.shade200,
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                  errorWidget: Container(
                    color: Colors.grey.shade200,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image_outlined,
                            size: 48,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '图片加载失败',
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),

        // 照片指示器
        if (_photos.length > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(_photos.length, (index) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        index == _currentPhotoIndex
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.5),
                  ),
                );
              }),
            ),
          ),
      ],
    );
  }

  /// 构建详情标签页
  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBasicInfo(),
          const SizedBox(height: 24),
          _buildLocationInfo(),
          const SizedBox(height: 24),
          _buildAdditionalInfo(),
        ],
      ),
    );
  }

  /// 构建照片标签页
  Widget _buildPhotosTab() {
    if (_isLoadingPhotos) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_photos.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.photo_library_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('暂无照片', style: TextStyle(color: Colors.grey, fontSize: 16)),
          ],
        ),
      );
    }

    return PhotoGallery(
      photos:
          _photos
              .map(
                (photo) => PhotoItem(
                  id: photo.id,
                  url: photo.url,
                  thumbnailUrl: photo.thumbnailUrl,
                  description: photo.description,
                ),
              )
              .toList(),
      config: PhotoGalleryConfig.grid(crossAxisCount: 2, enableViewer: true),
    );
  }

  /// 构建评论标签页
  Widget _buildCommentsTab() {
    return CommentSystem(
      targetId: widget.spot.id,
      type: CommentType.spot,
      // 移除标题以减少上方留白
      title: null,
    );
  }

  /// 构建基本信息
  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  '基本信息',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              '钓点类型',
              _getSpotTypeName(widget.spot.spotType),
              widget.spot.spotEmoji ?? '📍',
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              '鱼类类型',
              _getFishTypeName(widget.spot.fishTypes),
              widget.spot.fishEmoji ?? '🐟',
            ),
            if (widget.spot.description.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildInfoRow('描述', widget.spot.description, '📝'),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建位置信息
  Widget _buildLocationInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.location_on, color: Colors.red),
                const SizedBox(width: 8),
                const Text(
                  '位置信息',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              '坐标位置',
              '${widget.spot.locationLatLng.latitude.toStringAsFixed(6)}, ${widget.spot.locationLatLng.longitude.toStringAsFixed(6)}',
              '📍',
            ),
            if (widget.spot.address != null &&
                widget.spot.address!.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildInfoRow('详细地址', widget.spot.address!, '🏠'),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建附加信息
  Widget _buildAdditionalInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.more_horiz, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  '其他信息',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('创建者', widget.spot.userName ?? '未知', '👤'),
            const SizedBox(height: 12),
            _buildInfoRow(
              '可见性',
              _getVisibilityName(widget.spot.visibility.toString()),
              '👁️',
            ),
            const SizedBox(height: 12),
            _buildInfoRow('创建时间', _formatDateTime(widget.spot.created), '📅'),
          ],
        ),
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value, String emoji) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(emoji, style: const TextStyle(fontSize: 16)),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(fontSize: 14, color: Colors.black87),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200, width: 1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            // 点赞
            _buildInteractionButton(
              icon: FontAwesomeIcons.thumbsUp,
              label: '点赞',
              count: _likesCount,
              color: Colors.blue,
              isActive: _isLiked,
              onTap: _handleLike,
            ),

            // 不喜欢
            _buildInteractionButton(
              icon: FontAwesomeIcons.thumbsDown,
              label: '不喜欢',
              count: _unlikesCount,
              color: Colors.red,
              isActive: _isUnliked,
              onTap: _handleDislike,
            ),

            // 评论
            _buildInteractionButton(
              icon: FontAwesomeIcons.comment,
              label: '评论',
              count: _commentsCount,
              color: Colors.green,
              isActive: false,
              onTap: () {
                // 切换到评论标签页
                _tabController.animateTo(2);
              },
            ),

            // 导航
            _buildInteractionButton(
              icon: FontAwesomeIcons.locationArrow,
              label: '导航',
              count: null,
              color: Colors.purple,
              isActive: false,
              onTap: _handleNavigation,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建交互按钮
  Widget _buildInteractionButton({
    required IconData icon,
    required String label,
    required int? count,
    required Color color,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? color.withValues(alpha: 0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FaIcon(
              icon,
              size: 18,
              color: isActive ? color : Colors.grey.shade600,
            ),
            if (count != null) ...[
              const SizedBox(width: 6),
              Text(
                '$count',
                style: TextStyle(
                  fontSize: 14,
                  color: isActive ? color : Colors.grey.shade600,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ] else ...[
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: isActive ? color : Colors.grey.shade600,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 处理点赞
  Future<void> _handleLike() async {
    try {
      debugPrint('🔍 [点赞] 开始处理点赞操作');

      bool success = false;

      if (_isLiked) {
        // 如果已经点赞，则取消点赞
        success = await Services.spotInteraction.cancelLike(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isLiked = false;
            _likesCount = (_likesCount - 1).clamp(0, double.infinity).toInt();
          });
          debugPrint('✅ [点赞] 取消点赞成功');
        }
      } else {
        // 如果未点赞，则点赞
        success = await Services.spotInteraction.likeSpot(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isLiked = true;
            // 点赞时自动取消倒赞
            if (_isUnliked) {
              _isUnliked = false;
              _unlikesCount =
                  (_unlikesCount - 1).clamp(0, double.infinity).toInt();
            }
            _likesCount = _likesCount + 1;
          });
          debugPrint('✅ [点赞] 点赞成功');
        }
      }

      if (!success) {
        debugPrint('❌ [点赞] 点赞操作失败');
        if (mounted) {
          SnackBarService.showError(context, '操作失败，请稍后重试');
        }
      }
    } catch (e) {
      debugPrint('❌ [点赞] 点赞操作异常: $e');
      if (mounted) {
        SnackBarService.showError(context, '操作失败，请稍后重试');
      }
    }
  }

  /// 处理倒赞
  Future<void> _handleDislike() async {
    try {
      debugPrint('🔍 [倒赞] 开始处理倒赞操作');

      bool success = false;

      if (_isUnliked) {
        // 如果已经倒赞，则取消倒赞
        success = await Services.spotInteraction.cancelUnlike(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isUnliked = false;
            _unlikesCount =
                (_unlikesCount - 1).clamp(0, double.infinity).toInt();
          });
          debugPrint('✅ [倒赞] 取消倒赞成功');
        }
      } else {
        // 如果未倒赞，则倒赞
        success = await Services.spotInteraction.unlikeSpot(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isUnliked = true;
            // 倒赞时自动取消点赞
            if (_isLiked) {
              _isLiked = false;
              _likesCount = (_likesCount - 1).clamp(0, double.infinity).toInt();
            }
            _unlikesCount = _unlikesCount + 1;
          });
          debugPrint('✅ [倒赞] 倒赞成功');
        }
      }

      if (!success) {
        debugPrint('❌ [倒赞] 倒赞操作失败');
        if (mounted) {
          SnackBarService.showError(context, '操作失败，请稍后重试');
        }
      }
    } catch (e) {
      debugPrint('❌ [倒赞] 倒赞操作异常: $e');
      if (mounted) {
        SnackBarService.showError(context, '操作失败，请稍后重试');
      }
    }
  }

  /// 处理收藏
  Future<void> _handleFavorite() async {
    try {
      debugPrint('🔖 [收藏] 开始处理收藏操作');

      // 检查用户登录状态
      final currentUser = Services.auth.currentUser;
      if (currentUser == null) {
        debugPrint('❌ [收藏] 用户未登录');
        if (mounted) {
          SnackBarService.showError(context, '用户未登录，无法添加收藏');
        }
        return;
      }

      final success = await Services.userFavorite.toggleFavorite(
        widget.spot.id,
      );

      if (success && mounted) {
        setState(() {
          _isFavorited = Services.userFavorite.isFavorited(widget.spot.id);
        });

        // 显示成功提示
        SnackBarService.showSuccess(context, _isFavorited ? '已添加到收藏' : '已取消收藏');

        debugPrint('✅ [收藏] 收藏操作成功: ${_isFavorited ? "已收藏" : "已取消"}');
      } else {
        debugPrint('❌ [收藏] 收藏操作失败');
        if (mounted) {
          SnackBarService.showError(context, '操作失败，请稍后重试');
        }
      }
    } catch (e) {
      debugPrint('❌ [收藏] 收藏操作异常: $e');
      if (mounted) {
        SnackBarService.showError(context, '操作失败，请稍后重试');
      }
    }
  }

  /// 处理导航
  Future<void> _handleNavigation() async {
    try {
      debugPrint('🗺️ [导航] 开始导航到钓点: ${widget.spot.name}');

      // 调用百度地图导航服务
      await BaiduMapNavigationService.navigateToSpot(widget.spot, context);
    } catch (e) {
      debugPrint('❌ [导航] 导航失败: $e');
      if (mounted) {
        SnackBarService.showError(context, '导航失败，请重试');
      }
    }
  }

  /// 处理分享
  Future<void> _handleShare() async {
    try {
      debugPrint('🔗 [分享] 开始生成分享内容');

      // 获取钓点类型和鱼类信息
      final spotTypeName = _getSpotTypeName(widget.spot.spotType);
      final fishTypeName = _getFishTypeName(widget.spot.fishTypes);

      // 使用ShareService生成分享文本
      final shareText = Services.share.generateSpotShareText(
        spotId: widget.spot.id,
        spotName: widget.spot.name,
        spotType: spotTypeName,
        fishTypes: fishTypeName,
        likesCount: _likesCount,
        commentsCount: _commentsCount,
      );

      // 复制到剪贴板
      final success = await Services.share.copyToClipboard(shareText);

      // 显示结果提示
      if (mounted) {
        if (success) {
          SnackBarService.showSuccess(context, '分享内容已复制到剪贴板！\n快去分享给朋友吧 🎣');
        } else {
          SnackBarService.showError(context, '分享失败，请稍后重试');
        }
      }

      debugPrint('✅ [分享] 分享处理完成');
    } catch (e) {
      debugPrint('❌ [分享] 分享失败: $e');
      if (mounted) {
        SnackBarService.showError(context, '分享失败，请稍后重试');
      }
    }
  }

  /// 开始自动播放
  void _startAutoPlay() {
    if (_photos.length <= 1) return;

    _autoPlayTimer?.cancel();
    _autoPlayTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (!_userInteracted && _photoPageController.hasClients && mounted) {
        final nextIndex = (_currentPhotoIndex + 1) % _photos.length;
        _photoPageController.animateToPage(
          nextIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        _currentPhotoIndex = nextIndex;
      }
    });
  }

  /// 停止自动播放
  void _stopAutoPlay() {
    _autoPlayTimer?.cancel();
  }

  /// 用户手动滑动时的处理
  void _onUserSwipe() {
    _userInteracted = true;
    _stopAutoPlay();

    // 10秒后恢复自动播放
    Timer(const Duration(seconds: 10), () {
      if (mounted) {
        _userInteracted = false;
        _startAutoPlay();
      }
    });
  }

  /// 页面变化时的处理
  void _onPhotoPageChanged(int index) {
    if (mounted) {
      setState(() {
        _currentPhotoIndex = index;
      });
    }
  }

  /// 显示照片查看器
  void _showPhotoViewer(int initialIndex) {
    // 暂停自动播放
    _stopAutoPlay();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => PhotoViewer(
              photos:
                  _photos
                      .map(
                        (photo) => PhotoItem(
                          id: photo.id,
                          url: photo.url,
                          thumbnailUrl: photo.thumbnailUrl,
                          description: photo.description,
                        ),
                      )
                      .toList(),
              initialIndex: initialIndex,
            ),
      ),
    ).then((_) {
      // 返回时恢复自动播放
      if (_photos.length > 1) {
        _startAutoPlay();
      }
    });
  }

  /// 获取钓点类型名称
  String _getSpotTypeName(String? spotType) {
    if (spotType == null || spotType.isEmpty) return '未知';

    switch (spotType) {
      case 'river':
        return '河流';
      case 'lake':
        return '湖泊';
      case 'sea':
        return '海洋';
      case 'pond':
        return '池塘';
      case 'reservoir':
        return '水库';
      default:
        return spotType;
    }
  }

  /// 获取鱼类类型名称
  String _getFishTypeName(String? fishTypes) {
    if (fishTypes == null || fishTypes.isEmpty) return '未知';

    // 如果是单个字符串，尝试按逗号分割
    final typeList = fishTypes.split(',').map((e) => e.trim()).toList();

    final typeNames =
        typeList.map((type) {
          switch (type) {
            case 'carp':
              return '鲤鱼';
            case 'crucian':
              return '鲫鱼';
            case 'grass_carp':
              return '草鱼';
            case 'silver_carp':
              return '鲢鱼';
            case 'bighead_carp':
              return '鳙鱼';
            case 'bass':
              return '鲈鱼';
            case 'catfish':
              return '鲶鱼';
            case 'snakehead':
              return '黑鱼';
            default:
              return type;
          }
        }).toList();

    return typeNames.join('、');
  }

  /// 获取可见性名称
  String _getVisibilityName(String? visibility) {
    if (visibility == null || visibility.isEmpty) return '未知';

    switch (visibility) {
      case 'public':
        return '公开';
      case 'private':
        return '私有';
      case 'friends':
        return '好友可见';
      default:
        return visibility;
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '未知';

    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}

/// TabBar代理类，用于SliverPersistentHeader
class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _TabBarDelegate({required this.tabBar});

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(color: Colors.white, child: tabBar);
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
