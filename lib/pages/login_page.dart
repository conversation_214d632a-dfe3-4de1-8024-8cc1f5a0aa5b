import 'package:flutter/material.dart';
import '../services/service_locator.dart';
import 'user_agreement_page.dart';
import '../widgets/snackbar.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  // 服务 - 使用新的服务架构
  // 通过Services便捷访问器访问服务

  // 登录相关控制器和状态
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _confirmPasswordFocusNode = FocusNode();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;
  bool _isRegisterMode = false; // 是否为注册模式
  final bool _isPhoneLoginMode = false; // 是否为手机号登录模式（预留）
  bool _agreeToTerms = false; // 是否同意用户协议

  // 错误信息
  String? _emailError;
  String? _passwordError;
  String? _confirmPasswordError;

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
    _loadSavedCredentials();
    _loadAutoLoginSetting();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  // 检查登录状态
  Future<void> _checkLoginStatus() async {
    if (Services.auth.isLoggedIn) {
      if (mounted) {
        Navigator.pop(context);
      }
    }
  }

  // 加载保存的凭据
  Future<void> _loadSavedCredentials() async {
    try {
      // 使用 AuthService 的方法获取保存的登录信息
      final savedInfo = await Services.auth.getSavedLoginInfo();
      final savedEmail = savedInfo['email'];

      if (savedEmail != null && savedEmail.isNotEmpty) {
        _emailController.text = savedEmail;
      }
    } catch (e) {
      debugPrint('加载保存的凭据失败: $e');
    }
  }

  // 加载自动登录设置（记住登录状态默认开启，无需用户选择）
  Future<void> _loadAutoLoginSetting() async {
    // 记住登录状态现在是默认行为，不需要用户选择
    debugPrint('自动登录已默认开启');
  }

  // 登录/注册逻辑
  Future<void> _handleLogin() async {
    final email = _emailController.text.trim();
    final password = _passwordController.text.trim();
    final confirmPassword = _confirmPasswordController.text.trim();

    // 清除之前的错误
    setState(() {
      _emailError = null;
      _passwordError = null;
      _confirmPasswordError = null;
    });

    // 验证邮箱
    if (email.isEmpty) {
      setState(() {
        _emailError = '请输入邮箱';
      });
      return;
    }

    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      setState(() {
        _emailError = '请输入有效的邮箱地址';
      });
      return;
    }

    // 验证密码
    if (password.isEmpty) {
      setState(() {
        _passwordError = '请输入密码';
      });
      return;
    }

    // 如果是注册模式，验证确认密码和用户协议
    if (_isRegisterMode) {
      if (password.length < 6) {
        setState(() {
          _passwordError = '密码长度不能少于6位';
        });
        return;
      }

      if (confirmPassword.isEmpty) {
        setState(() {
          _confirmPasswordError = '请确认密码';
        });
        return;
      }

      if (password != confirmPassword) {
        setState(() {
          _confirmPasswordError = '两次输入的密码不一致';
        });
        return;
      }

      // 检查是否同意用户协议
      if (!_agreeToTerms) {
        SnackBarService.showError(context, '请先阅读并同意用户服务协议');
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = _isPhoneLoginMode
          ? await Services.auth.phoneLogin(
              phoneNumber: _phoneController.text.trim(),
              rememberCredentials: true, // 默认记住登录状态
            )
          : _isRegisterMode
              ? await Services.auth.register(
                  email: email,
                  password: password,
                  name: email.split('@')[0], // 使用邮箱前缀作为姓名
                  bio: '',
                )
              : await Services.auth.login(
                  email: email,
                  password: password,
                  rememberCredentials: true, // 默认记住登录状态
                );

      if (user != null && mounted) {
        if (mounted) {
          // 显示成功消息
          SnackBarService.showSuccess(
            context,
            _isRegisterMode ? '注册成功' : '登录成功',
          );

          // 检查是否可以返回到上一页（即是否是从其他页面跳转过来的）
          if (Navigator.canPop(context)) {
            // 如果可以返回，则返回登录成功的结果
            Navigator.pop(context, true);
          } else {
            // 如果不能返回，则跳转到主页
            Navigator.pushReplacementNamed(context, '/main');
          }
        }
      } else if (mounted) {
        // 显示失败消息
        SnackBarService.showError(
          context,
          _isRegisterMode ? '注册失败，请稍后重试' : '登录失败，请稍后重试',
        );
      }
    } catch (e) {
      if (mounted) {
        final errorMessage = e.toString();
        String displayMessage = '';

        if (_isRegisterMode) {
          // 注册错误处理
          if (errorMessage.contains('用户名已被使用') || errorMessage.contains('username')) {
            displayMessage = '用户名已被使用，请尝试其他邮箱';
          } else if (errorMessage.contains('邮箱已被使用') || errorMessage.contains('email')) {
            displayMessage = '邮箱已被使用，请直接登录或使用其他邮箱';
          } else if (errorMessage.contains('格式错误') || errorMessage.contains('400')) {
            displayMessage = '注册信息格式错误，请检查输入';
          } else {
            displayMessage = '注册失败，请稍后重试';
          }
        } else {
          // 登录错误处理
          if (errorMessage.contains('Invalid login credentials') ||
              errorMessage.contains('Failed to authenticate') ||
              errorMessage.contains('400')) {
            displayMessage = '邮箱或密码错误';
          } else if (errorMessage.contains('Email not confirmed')) {
            displayMessage = '邮箱未验证，请检查邮箱';
          } else {
            displayMessage = '登录失败，请稍后重试';
          }
        }

        // 显示错误消息
        SnackBarService.showError(context, displayMessage);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 切换密码可见性
  void _togglePasswordVisibility() {
    setState(() {
      _obscurePassword = !_obscurePassword;
    });
  }

  // 切换确认密码可见性
  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _obscureConfirmPassword = !_obscureConfirmPassword;
    });
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0088FF), // 主蓝色
              Color(0xFF00D4AA), // 青色
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
                child: Container(
                  constraints: const BoxConstraints(
                    maxWidth: 400,
                    maxHeight: 600,
                  ),
                  child: Container(
                      margin: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                            // 模式切换标题
                            Text(
                              _isRegisterMode ? '创建账户' : '登录账户',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF333333),
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 20),

                            // 邮箱输入框
                            _buildModernInputField(
                              controller: _emailController,
                              focusNode: _emailFocusNode,
                              hintText: '邮箱地址',
                              icon: Icons.email_outlined,
                              errorText: _emailError,
                              keyboardType: TextInputType.emailAddress,
                              onSubmitted: () => _passwordFocusNode.requestFocus(),
                            ),
                            const SizedBox(height: 20),

                            // 密码输入框
                            _buildModernInputField(
                              controller: _passwordController,
                              focusNode: _passwordFocusNode,
                              hintText: '密码',
                              icon: Icons.lock_outline,
                              errorText: _passwordError,
                              obscureText: _obscurePassword,
                              onToggleVisibility: _togglePasswordVisibility,
                              onSubmitted: _isRegisterMode
                                  ? () => _confirmPasswordFocusNode.requestFocus()
                                  : _handleLogin,
                              textInputAction: _isRegisterMode
                                  ? TextInputAction.next
                                  : TextInputAction.done,
                            ),


                            // 确认密码输入框（仅在注册模式显示）
                            if (_isRegisterMode) ...[
                              const SizedBox(height: 20),
                              _buildModernInputField(
                                controller: _confirmPasswordController,
                                focusNode: _confirmPasswordFocusNode,
                                hintText: '确认密码',
                                icon: Icons.lock_outline,
                                errorText: _confirmPasswordError,
                                obscureText: _obscureConfirmPassword,
                                onToggleVisibility: _toggleConfirmPasswordVisibility,
                                onSubmitted: _handleLogin,
                                textInputAction: TextInputAction.done,
                              ),
                            ],

                            // 用户协议（仅在注册模式显示）
                            if (_isRegisterMode) ...[
                              const SizedBox(height: 20),
                              Row(
                                children: [
                                  Checkbox(
                                    value: _agreeToTerms,
                                    onChanged: (value) {
                                      setState(() {
                                        _agreeToTerms = value ?? false;
                                      });
                                    },
                                    activeColor: const Color(0xFF0088FF),
                                  ),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _agreeToTerms = !_agreeToTerms;
                                        });
                                      },
                                      child: RichText(
                                        text: TextSpan(
                                          style: const TextStyle(
                                            fontSize: 14,
                                            color: Color(0xFF666666),
                                          ),
                                          children: [
                                            const TextSpan(text: '我已阅读并同意'),
                                            WidgetSpan(
                                              child: GestureDetector(
                                                onTap: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) => const UserAgreementPage(),
                                                    ),
                                                  );
                                                },
                                                child: const Text(
                                                  '《用户服务协议》',
                                                  style: TextStyle(
                                                    color: Color(0xFF0088FF),
                                                    decoration: TextDecoration.underline,
                                                    fontSize: 14,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],

                            const SizedBox(height: 20),

                            // 登录/注册按钮
                            Container(
                              width: double.infinity,
                              height: 50,
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [Color(0xFF0088FF), Color(0xFF00D4AA)],
                                ),
                                borderRadius: BorderRadius.circular(25),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0xFF0088FF).withValues(alpha: 0.3),
                                    blurRadius: 10,
                                    offset: const Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: ElevatedButton(
                                onPressed: _isLoading ? null : _handleLogin,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  shadowColor: Colors.transparent,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(25),
                                  ),
                                ),
                                child: _isLoading
                                    ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: Colors.white,
                                        ),
                                      )
                                    : Text(
                                        _isRegisterMode ? '注册账户' : '立即登录',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.white,
                                        ),
                                      ),
                              ),
                            ),

                            const SizedBox(height: 15),

                            // 模式切换
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  _isRegisterMode ? '已有账户？' : '还没有账户？',
                                  style: const TextStyle(
                                    color: Color(0xFF666666),
                                    fontSize: 14,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    setState(() {
                                      _isRegisterMode = !_isRegisterMode;
                                      _agreeToTerms = false;
                                      // 清除错误信息
                                      _emailError = null;
                                      _passwordError = null;
                                      _confirmPasswordError = null;
                                    });
                                  },
                                  child: Text(
                                    _isRegisterMode ? '立即登录' : '立即注册',
                                    style: const TextStyle(
                                      color: Color(0xFF0088FF),
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            // 预留第三方登录区域
                            const SizedBox(height: 15),
                            _buildThirdPartyLoginSection(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
          ),
      ),
    );
  }

  /// 构建现代化输入框
  Widget _buildModernInputField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String hintText,
    required IconData icon,
    String? errorText,
    bool obscureText = false,
    VoidCallback? onToggleVisibility,
    TextInputType keyboardType = TextInputType.text,
    TextInputAction textInputAction = TextInputAction.next,
    VoidCallback? onSubmitted,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: focusNode.hasFocus
                  ? const Color(0xFF0088FF)
                  : Colors.transparent,
              width: 2,
            ),
          ),
          child: TextField(
            controller: controller,
            focusNode: focusNode,
            obscureText: obscureText,
            keyboardType: keyboardType,
            textInputAction: textInputAction,
            onSubmitted: (_) => onSubmitted?.call(),
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF333333),
            ),
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: const TextStyle(
                color: Color(0xFF999999),
                fontSize: 16,
              ),
              prefixIcon: Icon(
                icon,
                color: const Color(0xFF666666),
                size: 20,
              ),
              suffixIcon: onToggleVisibility != null
                  ? IconButton(
                      icon: Icon(
                        obscureText ? Icons.visibility_off : Icons.visibility,
                        color: const Color(0xFF666666),
                        size: 20,
                      ),
                      onPressed: onToggleVisibility,
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          ),
        ),
        if (errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 8, left: 16),
            child: Text(
              errorText,
              style: const TextStyle(
                color: Color(0xFFFF4757),
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建第三方登录区域（预留）
  Widget _buildThirdPartyLoginSection() {
    return Column(
      children: [
        // 分割线
        Row(
          children: [
            const Expanded(child: Divider(color: Color(0xFFE0E0E0))),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '其他登录方式',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ),
            const Expanded(child: Divider(color: Color(0xFFE0E0E0))),
          ],
        ),

        const SizedBox(height: 20),

        // 第三方登录按钮（预留位置）
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildThirdPartyButton(
              icon: Icons.phone_android,
              label: '手机登录',
              onTap: () {
                // TODO: 实现手机登录
                SnackBarService.showInfo(context, '手机登录功能即将上线');
              },
            ),
            _buildThirdPartyButton(
              icon: Icons.wechat,
              label: '微信登录',
              onTap: () {
                // TODO: 实现微信登录
                SnackBarService.showInfo(context, '微信登录功能即将上线');
              },
            ),
            _buildThirdPartyButton(
              icon: Icons.more_horiz,
              label: '更多方式',
              onTap: () {
                // TODO: 实现更多登录方式
                SnackBarService.showInfo(context, '更多登录方式即将上线');
              },
            ),
          ],
        ),
      ],
    );
  }

  /// 构建第三方登录按钮
  Widget _buildThirdPartyButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFE0E0E0)),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 24,
              color: const Color(0xFF666666),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 10,
                color: Color(0xFF666666),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
