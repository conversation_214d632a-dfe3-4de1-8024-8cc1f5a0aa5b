# 钓点照片权限修复方案

## 问题描述

未登录用户无法显示钓点详情页的图片，只有登录用户才能显示。但根据app的功能需求，未登录用户也应该能显示公开钓点详情页中的图片。

## 问题根因

当前服务器上 `spot_photos` 集合的权限规则要求用户必须登录：

```json
{
  "listRule": "@request.auth.id != \"\"",
  "viewRule": "@request.auth.id != \"\""
}
```

这导致未登录用户无法访问任何钓点照片，包括公开钓点的照片。

## 解决方案

### 1. 更新权限规则

将 `spot_photos` 集合的权限规则修改为：

```json
{
  "listRule": "@request.auth.id != \"\" || spot_id.visibility = 'PUBLIC'",
  "viewRule": "@request.auth.id != \"\" || spot_id.visibility = 'PUBLIC'"
}
```

这样的权限规则意味着：
- ✅ 已登录用户可以查看所有照片（包括自己的私有钓点照片）
- ✅ 未登录用户只能查看公开钓点的照片
- ✅ 私有钓点的照片仍然受到保护

### 2. 实施步骤

#### 方法一：使用提供的脚本（推荐）

1. 运行权限更新脚本：
   ```bash
   chmod +x scripts/update_spot_photos_permissions.sh
   ./scripts/update_spot_photos_permissions.sh
   ```

#### 方法二：手动调用API

1. 调用权限更新API：
   ```bash
   curl -X POST http://*************:8090/api/admin/update-spot-photos-permissions
   ```

#### 方法三：通过PocketBase管理界面

1. 登录PocketBase管理界面
2. 进入 Collections → spot_photos
3. 点击 "API Rules" 标签
4. 修改 List Rule 和 View Rule 为：
   ```
   @request.auth.id != "" || spot_id.visibility = 'PUBLIC'
   ```
5. 保存更改

### 3. 验证修复

修复完成后，可以通过以下方式验证：

1. **应用测试**：
   - 退出登录状态
   - 查看公开钓点的详情页
   - 确认图片能正常显示

2. **日志检查**：
   - 查看应用日志中是否还有403权限错误
   - 确认图片加载成功的日志

3. **API测试**：
   ```bash
   # 测试未登录用户访问公开钓点照片
   curl "http://*************:8090/api/collections/spot_photos/records?filter=spot_id.visibility='PUBLIC'"
   ```

## 技术细节

### 权限规则语法说明

PocketBase的权限规则使用类似SQL的语法：

- `@request.auth.id != ""` - 检查用户是否已登录
- `spot_id.visibility = 'PUBLIC'` - 检查关联钓点的可见性
- `||` - 逻辑或操作符

### 相关代码修改

1. **pb_hooks/update_spot_photos_permissions.pb.js** - 权限更新Hook
2. **lib/widgets/spot_details_sheet.dart** - 改进错误处理和日志
3. **lib/services/service_locator.dart** - 确保R2只读凭据初始化

### 安全考虑

- ✅ 私有钓点照片仍然受保护
- ✅ 只有钓点所有者可以创建/修改/删除照片
- ✅ 未登录用户只能查看，不能操作
- ✅ 权限检查基于钓点的可见性设置

## 故障排除

### 如果修复后仍然无法显示图片

1. **检查权限规则是否生效**：
   ```bash
   curl "http://*************:8090/api/collections/spot_photos/records"
   ```

2. **检查R2凭据**：
   - 确认应用启动时成功初始化了只读R2凭据
   - 查看相关日志

3. **检查钓点可见性**：
   - 确认测试的钓点确实是PUBLIC可见性
   - 检查数据库中的visibility字段

4. **清除缓存**：
   - 重启应用
   - 清除图片缓存

## 相关文件

- `pb_hooks/update_spot_photos_permissions.pb.js` - 权限更新Hook
- `scripts/update_spot_photos_permissions.sh` - 权限更新脚本
- `lib/widgets/spot_details_sheet.dart` - 钓点详情页组件
- `lib/services/service_locator.dart` - 服务初始化
- `lib/services/encrypted_r2_service.dart` - R2凭据服务