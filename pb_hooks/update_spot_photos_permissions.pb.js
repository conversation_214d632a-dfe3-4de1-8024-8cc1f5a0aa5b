// 更新 spot_photos 集合权限规则
// 允许未登录用户查看公开钓点的照片

routerAdd("POST", "/api/admin/update-spot-photos-permissions", (c) => {
  console.log('收到更新spot_photos权限的请求');
  
  try {
    // 获取 spot_photos 集合
    const collection = $app.dao().findCollectionByNameOrId("spot_photos");
    
    if (!collection) {
      return c.json(404, {
        success: false,
        error: "spot_photos集合不存在"
      });
    }
    
    console.log('当前权限规则:');
    console.log('listRule:', collection.listRule);
    console.log('viewRule:', collection.viewRule);
    
    // 更新权限规则，允许未登录用户查看公开钓点的照片
    collection.listRule = "@request.auth.id != \"\" || spot_id.visibility = 'PUBLIC'";
    collection.viewRule = "@request.auth.id != \"\" || spot_id.visibility = 'PUBLIC'";
    
    // 保持其他权限规则不变
    // createRule, updateRule, deleteRule 仍然要求用户登录
    
    // 保存更新
    $app.dao().saveCollection(collection);
    
    console.log('权限规则更新成功:');
    console.log('新的listRule:', collection.listRule);
    console.log('新的viewRule:', collection.viewRule);
    
    return c.json(200, {
      success: true,
      message: "spot_photos权限规则更新成功",
      oldRules: {
        listRule: "@request.auth.id != \"\"",
        viewRule: "@request.auth.id != \"\""
      },
      newRules: {
        listRule: collection.listRule,
        viewRule: collection.viewRule
      }
    });
    
  } catch (error) {
    console.error('更新spot_photos权限失败:', error);
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('spot_photos权限更新脚本已加载');