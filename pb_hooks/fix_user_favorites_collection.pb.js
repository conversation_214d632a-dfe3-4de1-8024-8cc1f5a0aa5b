// 修复 user_favorites 集合权限
routerAdd("POST", "/api/admin/fix-user-favorites-collection", (c) => {
  console.log('收到修复user_favorites集合的请求');
  
  try {
    // 获取现有集合
    const collection = $app.dao().findCollectionByNameOrId("user_favorites");
    
    if (!collection) {
      return c.json(404, {
        success: false,
        error: "user_favorites集合不存在"
      });
    }
    
    console.log('当前权限规则:');
    console.log('listRule:', collection.listRule);
    console.log('viewRule:', collection.viewRule);
    console.log('createRule:', collection.createRule);
    console.log('updateRule:', collection.updateRule);
    console.log('deleteRule:', collection.deleteRule);
    
    // 设置正确的权限规则 - 用户只能管理自己的收藏
    collection.listRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    collection.viewRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    collection.createRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    collection.updateRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    collection.deleteRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    
    // 保存更新
    $app.dao().saveCollection(collection);
    
    console.log('权限规则更新成功:');
    console.log('新的listRule:', collection.listRule);
    console.log('新的viewRule:', collection.viewRule);
    console.log('新的createRule:', collection.createRule);
    console.log('新的updateRule:', collection.updateRule);
    console.log('新的deleteRule:', collection.deleteRule);
    
    return c.json(200, {
      success: true,
      message: "user_favorites集合权限修复成功",
      newRules: {
        listRule: collection.listRule,
        viewRule: collection.viewRule,
        createRule: collection.createRule,
        updateRule: collection.updateRule,
        deleteRule: collection.deleteRule
      }
    });
    
  } catch (error) {
    console.error('修复user_favorites集合失败:', error);
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('user_favorites集合权限修复脚本已加载');