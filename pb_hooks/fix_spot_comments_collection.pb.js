// 修复 spot_comments 集合权限和字段
routerAdd("POST", "/api/admin/fix-spot-comments-collection", (c) => {
  console.log('收到修复spot_comments集合的请求');
  
  try {
    // 获取现有集合
    const collection = $app.dao().findCollectionByNameOrId("spot_comments");
    
    if (!collection) {
      return c.json(404, {
        success: false,
        error: "spot_comments集合不存在"
      });
    }
    
    console.log('当前权限规则:');
    console.log('listRule:', collection.listRule);
    console.log('viewRule:', collection.viewRule);
    
    // 更新权限规则 - 允许未登录用户查看公开钓点的评论
    collection.listRule = "@request.auth.id != \"\" || spot_id.visibility = 'PUBLIC'";
    collection.viewRule = "@request.auth.id != \"\" || spot_id.visibility = 'PUBLIC'";
    collection.createRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    collection.updateRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    collection.deleteRule = "@request.auth.id != \"\" && user_id = @request.auth.id";
    
    // 检查并添加缺失的字段
    let needsUpdate = false;
    const currentSchema = collection.schema || [];
    
    // 检查是否有username字段
    const hasUsername = currentSchema.some(field => field.name === "username");
    if (!hasUsername) {
      currentSchema.push({
        name: "username",
        type: "text",
        required: true,
        options: {
          min: 1,
          max: 100,
          pattern: ""
        }
      });
      needsUpdate = true;
      console.log('添加username字段');
    }
    
    // 检查是否有created_at字段
    const hasCreatedAt = currentSchema.some(field => field.name === "created_at");
    if (!hasCreatedAt) {
      currentSchema.push({
        name: "created_at",
        type: "date",
        required: true,
        options: {
          min: "",
          max: ""
        }
      });
      needsUpdate = true;
      console.log('添加created_at字段');
    }
    
    // 更新schema
    if (needsUpdate) {
      collection.schema = currentSchema;
    }
    
    // 保存更新
    $app.dao().saveCollection(collection);
    
    console.log('权限规则和字段更新成功:');
    console.log('新的listRule:', collection.listRule);
    console.log('新的viewRule:', collection.viewRule);
    
    return c.json(200, {
      success: true,
      message: "spot_comments集合修复成功",
      changes: {
        permissions: "已更新权限规则",
        fields: needsUpdate ? "已添加缺失字段" : "字段无需更新"
      }
    });
    
  } catch (error) {
    console.error('修复spot_comments集合失败:', error);
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('spot_comments集合修复脚本已加载');